#include "tm1640.h"
#include "string.h"

/*******************************
*本程序直接驱动16位共阴数码管，各数码管分别驱动0、1、2、……E、F。
*/


#define clk TM1640DCLK  //时钟信号
#define dio TM1640DATA   //数据/地址数据
#define uchar unsigned char
#define uint unsigned int
#define DISP_LENGTH 20
#define DISP_WIDTH 5
#define TM1640DELAYCNT   1
#define TM1640MAXBYTE  14

//共阴极数码管
uchar tm1640reg[16];//1-9-F-不输出
//uchar weidata[4];//位变量
//uchar timecnt,keyzh,dpFlag = 0; //控制第二个数码管的dp的显示
//char time, sec;

void Delay_us(uint); //nus 延时
void tm1640_start(void);//1640开始
void tm1640_stop(void);  //1640停止
void tm1640_wrbyte(uchar); //写一个字节
void tm1640_display(void); //写显示寄存器 并开显示

static char tm1640brightness = 0;



const char bmp_ready[] = {0x77,0x32,0x55,0x51,0x55,0x73,0x57,0x52,0x51,0x25,0x75,0x35,0x2,0x0};
const char bmp_bye[]   = {0x9C,0xE8,0x41,0x52,0x2,0x1C,0xE2,0x40,0x22,0x2,0x1C,0xE2,0x1,0x0};
const char bmp_unit[] = {0xDF,0xD1,0x54,0x95,0x54,0xD5,0x49,0x55,0x51,0x54,0xD5,0xC5,0xC,0x0};



const char bmp_arrow[][TM1640MAXBYTE] = {
    {0x1,0x0,0x20,0x0,0x0,0x4,0x0,0x20,0x0,0x0,0x1,0x0,0x0,0x0},
    {0x2,0x0,0x40,0x0,0x0,0x8,0x0,0x40,0x0,0x0,0x2,0x0,0x0,0x0},
    {0x4,0x0,0x80,0x0,0x0,0x10,0x0,0x80,0x0,0x0,0x4,0x0,0x0,0x0},
    {0x8,0x0,0x0,0x1,0x0,0x20,0x0,0x0,0x1,0x0,0x8,0x0,0x0,0x0},
    {0x10,0x0,0x0,0x2,0x0,0x40,0x0,0x0,0x2,0x0,0x10,0x0,0x0,0x0},
    {0x20,0x0,0x0,0x4,0x0,0x80,0x0,0x0,0x4,0x0,0x20,0x0,0x0,0x0},
    {0x40,0x0,0x0,0x8,0x0,0x0,0x1,0x0,0x8,0x0,0x40,0x0,0x0,0x0},
    {0x80,0x0,0x0,0x10,0x0,0x0,0x2,0x0,0x10,0x0,0x80,0x0,0x0,0x0},//8
    {0x0,0x1,0x0,0x20,0x0,0x0,0x4,0x0,0x20,0x0,0x0,0x1,0x0,0x0},
    {0x0,0x2,0x0,0x40,0x0,0x0,0x8,0x0,0x40,0x0,0x0,0x2,0x0,0x0},
    {0x0,0x4,0x0,0x80,0x0,0x0,0x10,0x0,0x80,0x0,0x0,0x4,0x0,0x0},
    {0x0,0x8,0x0,0x0,0x1,0x0,0x20,0x0,0x0,0x1,0x0,0x8,0x0,0x0},
    {0x0,0x10,0x0,0x0,0x2,0x0,0x40,0x0,0x0,0x2,0x0,0x10,0x0,0x0},
    {0x0,0x20,0x0,0x0,0x4,0x0,0x80,0x0,0x0,0x4,0x0,0x20,0x0,0x0},
    {0x0,0x40,0x0,0x0,0x8,0x0,0x0,0x1,0x0,0x8,0x0,0x40,0x0,0x0},
    {0x0,0x80,0x0,0x0,0x10,0x0,0x0,0x2,0x0,0x10,0x0,0x80,0x0,0x0},//16
    {0x0,0x0,0x1,0x0,0x20,0x0,0x0,0x4,0x0,0x20,0x0,0x0,0x1,0x0},
    {0x0,0x0,0x2,0x0,0x40,0x0,0x0,0x8,0x0,0x40,0x0,0x0,0x2,0x0},
    {0x0,0x0,0x4,0x0,0x80,0x0,0x0,0x0,0x0,0x80,0x0,0x0,0x4,0x0},
    {0x0,0x0,0x8,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x8,0x0}
};
const char bmp_dot[][TM1640MAXBYTE] = {
    {0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x4,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x8,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x20,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x40,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x80,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x4,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x8,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x20,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x40,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x80,0x0,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x4,0x0,0x0,0x0,0x0,0x0,0x0},
    {0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x8,0x0,0x0,0x0,0x0,0x0,0x0}

};

const char bmp_P1[][TM1640MAXBYTE] = {
    {0x1C,0x0,0x40,0x1,0x0,0x14,0x0,0x40,0x1,0x0,0x1C,0x4,0x0,0x0},
    {0xC,0x0,0x80,0x0,0x0,0x8,0x0,0x80,0x0,0x0,0x1C,0x4,0x0,0x0},
    {0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x40,0x0,0x0,0x1C,0x4,0x0,0x0},
    {0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x4,0x0,0x0},
    {0x14,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x10,0x4,0x0,0x0},
    {0x1C,0x0,0x40,0x0,0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x4,0x0,0x0},
    {0x1C,0x0,0x40,0x0,0x0,0x1C,0x0,0x40,0x1,0x0,0x1C,0x4,0x0,0x0},
    {0x1C,0x0,0x0,0x1,0x0,0x10,0x0,0x0,0x1,0x0,0x10,0x4,0x0,0x0},
    {0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x40,0x1,0x0,0x1C,0x4,0x0,0x0},
    {0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x4,0x0,0x0}

};
const char bmp_P2[][TM1640MAXBYTE] = {
    {0xC0,0x1,0x0,0x14,0x0,0x40,0x1,0x0,0x14,0x0,0xC0,0x5,0x0,0x0},
    {0xC0,0x0,0x0,0x8,0x0,0x80,0x0,0x0,0x8,0x0,0xC0,0x5,0x0,0x0},
    {0xC0,0x1,0x0,0x10,0x0,0xC0,0x1,0x0,0x4,0x0,0xC0,0x5,0x0,0x0},
    {0xC0,0x1,0x0,0x10,0x0,0xC0,0x1,0x0,0x10,0x0,0xC0,0x5,0x0,0x0},
    {0x40,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x10,0x0,0x0,0x5,0x0,0x0},
    {0xC0,0x1,0x0,0x4,0x0,0xC0,0x1,0x0,0x10,0x0,0xC0,0x5,0x0,0x0},
    {0xC0,0x1,0x0,0x4,0x0,0xC0,0x1,0x0,0x14,0x0,0xC0,0x5,0x0,0x0},
    {0xC0,0x1,0x0,0x10,0x0,0x0,0x1,0x0,0x10,0x0,0x0,0x5,0x0,0x0},
    {0xC0,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x14,0x0,0xC0,0x5,0x0,0x0},
    {0xC0,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x10,0x0,0xC0,0x5,0x0,0x0}
};
const char bmp_P3[][TM1640MAXBYTE] = {
    {0x0,0x70,0x0,0x0,0x5,0x0,0x50,0x0,0x0,0x5,0x0,0x74,0x0,0x0},
    {0x0,0x30,0x0,0x0,0x2,0x0,0x20,0x0,0x0,0x2,0x0,0x74,0x0,0x0},
    {0x0,0x70,0x0,0x0,0x4,0x0,0x70,0x0,0x0,0x1,0x0,0x74,0x0,0x0},
    {0x0,0x70,0x0,0x0,0x4,0x0,0x70,0x0,0x0,0x4,0x0,0x74,0x0,0x0},
    {0x0,0x50,0x0,0x0,0x5,0x0,0x70,0x0,0x0,0x4,0x0,0x44,0x0,0x0},
    {0x0,0x70,0x0,0x0,0x1,0x0,0x70,0x0,0x0,0x4,0x0,0x74,0x0,0x0},
    {0x0,0x70,0x0,0x0,0x1,0x0,0x70,0x0,0x0,0x5,0x0,0x74,0x0,0x0},
    {0x0,0x70,0x0,0x0,0x4,0x0,0x40,0x0,0x0,0x4,0x0,0x44,0x0,0x0},
    {0x0,0x70,0x0,0x0,0x5,0x0,0x70,0x0,0x0,0x5,0x0,0x74,0x0,0x0},
    {0x0,0x70,0x0,0x0,0x5,0x0,0x70,0x0,0x0,0x4,0x0,0x74,0x0,0x0}
};
const char bmp_P4[][TM1640MAXBYTE] = {
    {0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x5,0x0,0x50,0x0,0x4,0x7,0x0},
    {0x0,0x0,0x3,0x0,0x20,0x0,0x0,0x2,0x0,0x20,0x0,0x4,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x7,0x0,0x10,0x0,0x4,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x7,0x0,0x40,0x0,0x4,0x7,0x0},
    {0x0,0x0,0x5,0x0,0x50,0x0,0x0,0x7,0x0,0x40,0x0,0x4,0x4,0x0},
    {0x0,0x0,0x7,0x0,0x10,0x0,0x0,0x7,0x0,0x40,0x0,0x4,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x10,0x0,0x0,0x7,0x0,0x50,0x0,0x4,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x4,0x0,0x40,0x0,0x4,0x4,0x0},
    {0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x7,0x0,0x50,0x0,0x4,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x7,0x0,0x40,0x0,0x4,0x7,0x0}
};
const char bmp_P4_1[][TM1640MAXBYTE] = {
    {0x1C,0x0,0x40,0x1,0x0,0x14,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x0},
    {0xC,0x0,0x80,0x0,0x0,0x8,0x0,0x80,0x0,0x0,0x1C,0x0,0x0,0x0},
    {0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x40,0x0,0x0,0x1C,0x0,0x0,0x0},
    {0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x0,0x0},
    {0x14,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x10,0x0,0x0,0x0},
    {0x1C,0x0,0x40,0x0,0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x0,0x0},
    {0x1C,0x0,0x40,0x0,0x0,0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x0},
    {0x1C,0x0,0x0,0x1,0x0,0x10,0x0,0x0,0x1,0x0,0x10,0x0,0x0,0x0},
    {0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x0},
    {0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x0,0x0}
};
const char bmp_P2_dot[][TM1640MAXBYTE] = {
    {0x0,0x1C,0x0,0x40,0x1,0x0,0x14,0x0,0x40,0x1,0x0,0x5C,0x0,0x0},
    {0x0,0xC,0x0,0x80,0x0,0x0,0x8,0x0,0x80,0x0,0x0,0x5C,0x0,0x0},
    {0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x40,0x0,0x0,0x5C,0x0,0x0},
    {0x0,0x1C,0x0,0x0,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x5C,0x0,0x0},
    {0x0,0x14,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x50,0x0,0x0},
    {0x0,0x1C,0x0,0x40,0x0,0x0,0x1C,0x0,0x0,0x1,0x0,0x5C,0x0,0x0}, //5
    {0x0,0x1C,0x0,0x40,0x0,0x0,0x1C,0x0,0x40,0x1,0x0,0x5C,0x0,0x0},
    {0x0,0x1C,0x0,0x0,0x1,0x0,0x10,0x0,0x0,0x1,0x0,0x50,0x0,0x0},
    {0x0,0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x40,0x1,0x0,0x5C,0x0,0x0},
    {0x0,0x1C,0x0,0x40,0x1,0x0,0x1C,0x0,0x0,0x1,0x0,0x5C,0x0,0x0}
};
const char bmp_P3_1[][TM1640MAXBYTE] = {
    {0xC0,0x1,0x0,0x14,0x0,0x40,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x0},
    {0xC0,0x0,0x0,0x8,0x0,0x80,0x0,0x0,0x8,0x0,0xC0,0x1,0x0,0x0},
    {0xC0,0x1,0x0,0x10,0x0,0xC0,0x1,0x0,0x4,0x0,0xC0,0x1,0x0,0x0},
    {0xC0,0x1,0x0,0x10,0x0,0xC0,0x1,0x0,0x10,0x0,0xC0,0x1,0x0,0x0},
    {0x40,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x10,0x0,0x0,0x1,0x0,0x0},
    {0xC0,0x1,0x0,0x4,0x0,0xC0,0x1,0x0,0x10,0x0,0xC0,0x1,0x0,0x0}, //5
    {0xC0,0x1,0x0,0x4,0x0,0xC0,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x0},
    {0xC0,0x1,0x0,0x10,0x0,0x0,0x1,0x0,0x10,0x0,0x0,0x1,0x0,0x0},
    {0xC0,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x0},
    {0xC0,0x1,0x0,0x14,0x0,0xC0,0x1,0x0,0x10,0x0,0xC0,0x1,0x0,0x0}
};
const char bmp_P1_1[][TM1640MAXBYTE] = {
    {0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x5,0x0,0x50,0x0,0x0,0x7,0x0},
    {0x0,0x0,0x3,0x0,0x20,0x0,0x0,0x2,0x0,0x20,0x0,0x0,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x7,0x0,0x10,0x0,0x0,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x7,0x0},
    {0x0,0x0,0x5,0x0,0x50,0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x4,0x0},
    {0x0,0x0,0x7,0x0,0x10,0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x7,0x0}, //5
    {0x0,0x0,0x7,0x0,0x10,0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x4,0x0,0x40,0x0,0x0,0x4,0x0},
    {0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x7,0x0},
    {0x0,0x0,0x7,0x0,0x50,0x0,0x0,0x7,0x0,0x40,0x0,0x0,0x7,0x0}
};
const char bmp_0_3[][TM1640MAXBYTE] = {
    {0x38,0xE8,0x80,0x42,0x8,0x28,0xE2,0x80,0x12,0x8,0xB8,0xE0,0x0,0x0},
    {0x18,0xE8,0x0,0x41,0x8,0x10,0xE2,0x0,0x11,0x8,0xB8,0xE0,0x0,0x0},
    {0x38,0xE8,0x0,0x42,0x8,0x38,0xE2,0x80,0x10,0x8,0xB8,0xE0,0x0,0x0}
};


const char bmp_n_5[][TM1640MAXBYTE] = {
    {0x18,0xE8,0x0,0x41,0x2,0x10,0xE2,0x0,0x11,0x8,0xB8,0xE0,0x0,0x0},
    {0x38,0xE8,0x0,0x42,0x2,0x38,0xE2,0x80,0x10,0x8,0xB8,0xE0,0x0,0x0},
    {0x38,0xE8,0x0,0x42,0x2,0x38,0xE2,0x0,0x12,0x8,0xB8,0xE0,0x0,0x0},
    {0x28,0xE8,0x80,0x42,0x2,0x38,0xE2,0x0,0x12,0x8,0xA0,0xE0,0x0,0x0},
    {0x28,0xE8,0x80,0x42,0x2,0x38,0xE2,0x0,0x12,0x8,0xA0,0xE0,0x0,0x0}

};

const char bmp_cal_1[] = {0x26,0xC1,0x10,0x15,0x8,0x51,0x99,0x10,0x17,0x8,0x56,0xC7,0x1,0x0};
const char bmp_cal_2[] = {0x26,0xC1,0x11,0x15,0x10,0x51,0xD9,0x11,0x17,0x4,0x56,0xC7,0x1,0x0};
const char bmp_cal_ok[] = {0x26,0x71,0x15,0x15,0x55,0x51,0x51,0x13,0x17,0x55,0x56,0x77,0x5,0x0};
const char bmp_test[] = {0xC8,0x70,0x47,0x9,0x44,0x94,0x70,0x47,0x9,0x41,0xC8,0x75,0x7,0x0};
const char bmp_E01[] = {0xE0,0x4E,0x0,0xA2,0x6,0xE0,0x4A,0x0,0xA2,0x4,0xE0,0xEE,0x0,0x0};

const char bmp_lock[] = {0x26,0x70,0x60,0x86,0xF,0xFF,0xDF,0x0,0x80,0xF,0x0,0x70,0x0,0x0};
const char bmp_nc[] = {0x0,0x6,0x0,0x10,0x0,0x78,0xF1,0x0,0x10,0x0,0x0,0x6,0x0,0x0};
const char bmp_lowp[] =	{0xF8,0xFF,0xC3,0x0,0x38,0xC,0x80,0xC3,0x0,0x38,0xF8,0xFF,0x3,0x0};
const char bmp_mg_dl[]  = {0xDF,0x91,0x52,0x95,0x28,0xD5,0xE9,0x52,0x51,0x2A,0xD5,0xE5,0xE,0x0};
const char bmp_umol[]  = {0x0,0x80,0xA0,0xBE,0xB,0xAA,0xAA,0xE0,0xAA,0x1B,0x2,0x0,0x0,0x0};

//static void tm1640_gpio_init(void)
//{
/*
	nrfx_err_t ret_code;
	nrf_drv_gpiote_in_config_t gpiote_config = GPIOTE_CONFIG_IN_SENSE_TOGGLE(true);
    gpiote_config.pull = NRF_GPIO_PIN_PULLDOWN;
    ret_code = nrf_drv_gpiote_in_init(TOUCH1_PIN, &gpiote_config, touch1_handler);
    APP_ERROR_CHECK(ret_code);
*/
//	nrf_gpio_cfg_output(TM1640DCLK);
//	nrf_gpio_cfg_output(TM1640DATA);
//}

static void inline tm1640CLK_H(void)
{

    nrf_gpio_pin_set(TM1640DCLK);

}

static void inline tm1640CLK_L(void)
{

    nrf_gpio_pin_clear(TM1640DCLK);
}

static void inline tm1640DATA_H(void)
{

    nrf_gpio_pin_set(TM1640DATA);

}

static void inline tm1640DATA_L(void)
{

    nrf_gpio_pin_clear(TM1640DATA);

}



///=======================================
static void tm1640_Delay_us(uint i) //nus 延时
{
    nrf_delay_us(i);
}
///======================================
static void tm1640_start(void) //1640开始
{
    tm1640CLK_H();
    tm1640DATA_H();
    tm1640_Delay_us(TM1640DELAYCNT);
    tm1640DATA_L();
    tm1640_Delay_us(TM1640DELAYCNT);
}

static void tm1640_stop(void)  //1640停止
{
    tm1640CLK_L();
    tm1640_Delay_us(TM1640DELAYCNT);
    tm1640DATA_L();
    tm1640_Delay_us(TM1640DELAYCNT);
    tm1640CLK_H();
    tm1640_Delay_us(TM1640DELAYCNT);
    tm1640DATA_H();
    tm1640_Delay_us(TM1640DELAYCNT);
}
//＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
void tm1640_wrbyte(unsigned char oneByte) //写一个字节
{
    uchar i;
    for(i=0; i<8; i++) {
        tm1640CLK_L();
        tm1640_Delay_us(TM1640DELAYCNT);
        if(oneByte&0x01) {
            tm1640DATA_H(); //低位在前
        } else {
            tm1640DATA_L();
        }
        tm1640_Delay_us(TM1640DELAYCNT);
        oneByte = oneByte>>1;
        tm1640CLK_H();
        tm1640_Delay_us(TM1640DELAYCNT);
    }
}
void tm1640_init(unsigned char bri)
{
    memset(tm1640reg,0,16);
    tm1640_clear();
    tm1640_brighteness(bri);
}
void tm1640_brighteness(unsigned char x)  // 0--8 grade
{
    if(x>0x08) {
        x = 0x08;
    }
    tm1640brightness = x;
    char reg = 0x88|tm1640brightness;
    tm1640_start();
    tm1640_wrbyte(reg);//开显示 ，亮度:14/16
    tm1640_stop();
}
void tm1640_disp_close(void)  // close disp
{

    char reg = 0x7F&tm1640brightness;
    tm1640_start();
    tm1640_wrbyte(reg);//开显示 ，亮度:14/16
    tm1640_stop();
}
void tm1640_disp_on(void)  // close disp
{
    char reg = 0x88|tm1640brightness;
    tm1640_start();
    tm1640_wrbyte(reg);//开显示 ，亮度:14/16
    tm1640_stop();
}

void tm1640_clear(void)
{
    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(uint32_t i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
}


//＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
void tm1640_show_ready(void) //写显示寄存器 并开显示
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = bmp_ready[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();

}

//＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
void tm1640_show_E01(void) //写显示寄存器 并开显示
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = bmp_E01[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();

}


void tm1640_show_bye(void) //写显示寄存器 并开显示
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_bye[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();

}
void tm1640_show_unit(unsigned int unit) //写显示寄存器 并开显示
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);

    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        if(unit != 0) {
            tm1640reg[i] = 	bmp_unit[i];
        } else {
            tm1640reg[i] = 	bmp_umol[i];
        }
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();

}

void tm1640_show_lock(void) //写显示寄存器 并开显示
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_lock[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();
}
void tm1640_show_nc(void) //写显示寄存器 并开显示
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	0xff;
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();
}

void tm1640_show_arrow(uchar val) //写显示寄存器 并开显示
{

    uchar i;
    if(val > 20) {
        NRF_LOG_INFO("ARROW ERROR");
    }
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_arrow[val][i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();

}

void tm1640_show_dot(uchar val) //写显示寄存器 并开显示
{

    uchar i;
    if(val > 20) {
        NRF_LOG_INFO("ARROW ERROR");
        return;
    }
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
//	memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] |= 	bmp_dot[val][i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();

}

void tm1640_show_n_3(uchar val) //写显示寄存器 并开显示
{

    uchar i;
    if(val > 3) {
        NRF_LOG_INFO("show n ERROR");
    }
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_0_3[val][i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
    tm1640_disp_on();
}
void tm1640_show_n_5(uchar val) //写显示寄存器 并开显示
{

    uchar i;
    if(val > 5) {
        NRF_LOG_INFO("show n ERROR");
    }
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_n_5[val][i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();

}
void tm1640_show_Result_2(unsigned char ten,unsigned char one,unsigned char f1, unsigned char f2)
{

    uchar i=0;

    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();
    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    if(ten == 0) {
        for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
            tm1640reg[i] |= bmp_P2[one][i];
            tm1640reg[i] |= bmp_P3[f1][i];
            tm1640reg[i] |= bmp_P4[f2][i];
            tm1640_wrbyte(tm1640reg[i]); //送数据
        }
    } else {
        for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
            tm1640reg[i] |= bmp_P1[ten][i];
            tm1640reg[i] |= bmp_P2[one][i];
            tm1640reg[i] |= bmp_P3[f1][i];
            tm1640reg[i] |= bmp_P4[f2][i];
            tm1640_wrbyte(tm1640reg[i]); //送数据
        }
    }
    tm1640_stop();
    tm1640_disp_on();
}
void tm1640_show_Result_3(unsigned char bai,unsigned char ten,unsigned char one,unsigned char f1)
{

    uchar i=0;

    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();
    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    if(bai == 0) {
        if(ten==0) {
            for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
                tm1640reg[i] = bmp_P2_dot[0][i];
                tm1640reg[i] |= bmp_P1_1[0][i];
                tm1640_wrbyte(tm1640reg[i]); //送数据
            }

        } else {
            for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
                tm1640reg[i] |= bmp_P3_1[ten][i];
                tm1640reg[i] |= bmp_P2_dot[one][i];
                tm1640reg[i] |= bmp_P1_1[f1][i];
                tm1640_wrbyte(tm1640reg[i]); //送数据
            }
        }
    } else {
        for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
            tm1640reg[i] |= bmp_P4_1[bai][i];
            tm1640reg[i] |= bmp_P3_1[ten][i];
            tm1640reg[i] |= bmp_P2_dot[one][i];
            tm1640reg[i] |= bmp_P1_1[f1][i];
            tm1640_wrbyte(tm1640reg[i]); //送数据
        }
    }
    tm1640_stop();
    tm1640_disp_on();
}

void tm1640_show_Result_1(unsigned char ten,unsigned char one,unsigned char f1)
{

    uchar i=0;
//	int32_t x,y,z;
//	x= a / 100;
//	y = a % 10;
    tm1640_clear();    //每次清屏
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();
    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    if(ten == 0) {
        for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
            tm1640reg[i] |= bmp_P2[one][i];
            tm1640reg[i] |= bmp_P3[f1][i];
            tm1640_wrbyte(tm1640reg[i]); //送数据
        }
    } else {
        for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
            tm1640reg[i] |= bmp_P1[ten][i];
            tm1640reg[i] |= bmp_P2[one][i];
            tm1640reg[i] |= bmp_P3[f1][i];
            tm1640_wrbyte(tm1640reg[i]); //送数据
        }
    }
    tm1640_stop();
    tm1640_disp_on();
}

void tm1640_show_char(unsigned char addr,unsigned val) //写显示寄存器
{

    uchar i;
    tm1640_clear();    //每次清屏
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();
    tm1640_start();
    tm1640_wrbyte(0xc0 + addr);//设置首地址

    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。

        tm1640_wrbyte(0xff); //送数据
    }


    tm1640_stop();
}

void tm1640_show_Batt_empty(void)
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_lowp[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();

}


void tm1640_show_mg_dl(void)
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_mg_dl[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
}
void tm1640_show_bmp_umol(void)
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_umol[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
}
void tm1640_show_cal_1(void)
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();
    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_cal_1[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
}
void tm1640_show_cal_2(void)
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_cal_2[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
}
void tm1640_show_cal_ok(void)
{
    uchar i;
    tm1640_start();
    tm1640_wrbyte(0x40);// 40H 地址自动加 1 模式,44H 固定地址模式,本程序采用自加 1 模式
    tm1640_stop();

    tm1640_start();
    tm1640_wrbyte(0xc0);//设置首地址
    memset(tm1640reg,0,16);
    for(i=0; i<TM1640MAXBYTE; i++) { //地址自加，不必每次都写地址。
        tm1640reg[i] = 	bmp_cal_ok[i];
        tm1640_wrbyte(tm1640reg[i]); //送数据
    }
    tm1640_stop();
}
