#include "motor.h"
#include "pwm.h"


#define MOTOR_STRENGTH  (75)


APP_TIMER_DEF(motor_timer_id);



//定时器回调
static void motor_timeout_handler(void * p_context)
{
    UNUSED_PARAMETER(p_context);

    pwm_motor_set_duty_percent(0);
}


//马达初始化
void motor_init(void)
{
    ret_code_t ret_code;

    ret_code = app_timer_create(&motor_timer_id, APP_TIMER_MODE_SINGLE_SHOT, motor_timeout_handler);
    APP_ERROR_CHECK(ret_code);
}


//马达震动一次
void motor_start_once(uint32_t ms)
{

    ret_code_t ret_code;

    ret_code = app_timer_stop(motor_timer_id);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(motor_timer_id, APP_TIMER_TICKS(ms), NULL);
    APP_ERROR_CHECK(ret_code);
    pwm_motor_set_duty_percent(MOTOR_STRENGTH);

}


//打开马达
void motor_start(void)
{
    pwm_motor_set_duty_percent(MOTOR_STRENGTH);
}


//停止马达
void motor_stop(void)
{
    pwm_motor_set_duty_percent(0);
}

