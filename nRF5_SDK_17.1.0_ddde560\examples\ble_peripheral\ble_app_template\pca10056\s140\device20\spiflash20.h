#ifndef __SPIFLASH20_H__
#define __SPIFLASH20_H__

//ZD25WD20B  2M-bit

//Flexible Architecture for Code and Data Storage
//- Uniform 256-byte PageProgram
//- Uniform 256-byte Page Erase
//- Uniform 4K-byte Sector Erase
//- Uniform 32K/64K-byte Block Erase

#include "main.h"
#include "nrf_drv_spi.h"

// 测量数据结构定义
typedef struct {
    uint32_t is_valid;
    uint32_t raw_data_batt;
    uint32_t raw_data_dark;
    uint32_t raw_data_b10;
    uint32_t raw_data_g100;
    uint32_t temperature;     // 温度数据 (摄氏度*1000倍，例如25.5°C存储为25500)
    uint32_t result;          // mg/dL单位*1000倍
    uint32_t stamp;
} meas_record_t;

//校准参数
typedef struct {
    uint32_t is_valid;//0表示未校准，1表示已经校准，但不知道是什么校准方式，2表示普通校准，3表示特殊校准

    //全反射时（白板）
    uint32_t d0;
    uint32_t g0;
    uint32_t b0;
    //无反射时（对空气）
    uint32_t d1;
    uint32_t g1;
    uint32_t b1;
    uint8_t g_pwm;  //divid G灯亮时 ADC值为1000左右时的PWM值，经ADC三倍放大
    uint8_t b_pwm;  //divid B灯亮时 ADC值为3000左右时的PWM值,未经放大
    uint8_t temph;
    uint8_t templ;
    //结果调整
    uint8_t result_ratio;//100是1倍，90是0.9倍
    uint8_t dumy0;// 预留，字节对齐
    uint8_t dumy1;//
    uint8_t dumy2;//

    uint32_t gotkb;	  //divid   表示k b是否存在，值为0x55则表示存在
    uint32_t ksign;   //divid   k的正负值 正为1，负值为0
    uint32_t k;       //divid   k的真实值X10000
    uint32_t bsign;	  //divid   b的正负值 正为1，负值为0
    uint32_t b;       //divid   b的真实值X10000
} cali_para_t;


// 错误日志记录结构
typedef struct {
    uint32_t error_code;    // 错误代码
    uint32_t timestamp;     // 时间戳
} error_log_record_t;

//flash 总：0x0 - 0x200000
//测量数据：0x4000 - 0x10000
// 测量数据存储配置
#define MAX_MEAS_RECORDS        1000
#define MEAS_RECORD_SIZE        sizeof(meas_record_t)  // 现在是32字节 (8个uint32_t字段)
#define MEAS_DATA_START_ADDR    0x4000  // 从16KB地址开始存储测量数据
#define MEAS_RECORDS_PER_SECTOR (4096 / MEAS_RECORD_SIZE)  /


//时间戳 设备锁状态
#define EXT_STAMP_SECTOR_ADDR   0x1000    // 时间戳扇区
#define EXT_LOCK_SECTOR_ADDR    0x2000    // 锁状态扇区  
#define EXT_META_SECTOR_ADDR    0x3000    // 序列号扇区 
#define EXT_AUTOLOCK_SECTOR_ADDR 0x3800  // 自动锁状态扇区 

// 比率参数存储区域
#define RATIO_SECTOR_ADDR       0x10000   // 比率参数扇区 (4KB)
#define RATIO_DATA_SIZE         256       // 比率数据大小 (1个uint8_t)

// PWM参数存储区域
#define PWM_SECTOR_ADDR         0x11000   // PWM参数扇区 (4KB)
#define PWM_DATA_SIZE           512       // PWM数据大小 (2个uint8_t)

// 校准参数存储区域
#define CALI_PARA_SECTOR_ADDR    0x12000   // 校准参数扇区 (4KB)
#define CALI_PARA_DATA_SIZE      64        // 校准参数数据大小 (46字节，对齐到64字节)

// 错误信息存储区域
#define ERROR_LOG_SECTOR_ADDR    0x13000   // 错误日志扇区 (4KB)
#define ERROR_LOG_RECORD_SIZE    8         // 每条错误记录大小 (uint32_t error_code + uint32_t timestamp)
#define MAX_ERROR_LOG_RECORDS    512       // 最大错误记录数 (4096 / 8 = 512)

// SWDOE状态存储区域
#define SWDOE_STATE_SECTOR_ADDR  0x15000   // SWDOE状态数据扇区 (4KB)
#define SWDOE_STATE_DATA_SIZE    1         // uint8_t数据大小

void spi_event_handler(nrf_drv_spi_evt_t const * p_event, void *p_context);

void spi_master_init(void);

/*
向flash中写入数据
参数  data 要写入的数据
*/
void spi_flash_write_reg(uint8_t data);

/*
从flash中读取数据
参数： reg 寄存器地址
*/
uint8_t spi_flash_read_reg(uint8_t reg);


/*
读取flash的器件ID
*/
uint32_t spi_flash_ReadID(void);


/*
写使能命令
*/
void spi_flash_WriteEnable(void);


/*
通过读状态寄存器等待FLASH芯片空闲
*/
void spi_flash_WaitForWriteEnd(void);

/*
擦除FLASH的扇区
参数 SectorAddr 要擦除的扇区地址
*/
void spi_flash_FLASH_SectorErase(uint32_t SectorAddr);


/*
FLASH页写入指令
参数：
备注：使用页写入指令最多可以一次向FLASH传输256个字节的数据
*/
void spi_flash_FLASH_PageWrite(unsigned char* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);

/*
从FLASH中读取数据
*/
void spi_flash_Flash_BufferRead(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead);

/*
全片擦除
*/
void spi_flash_Chip_Erase(void);



void spi_flash_init(void);

// 测量数据存储API
/*
写入测量数据到指定索引位置
参数：
  index: 数据索引
  data: 要写入的测量数据指针
返回值：
  0: 成功
  -1: 索引超出范围
  -2: Flash操作失败
*/
int meas_data_write(uint16_t index, const meas_record_t* data);

/*
从指定索引位置读取测量数据
参数：
  index: 数据索引
  data: 用于存储读取数据的缓冲区指针
返回值：
  0: 成功
  -1: 索引超出范围
  -2: Flash操作失败
  -3: 数据无效
*/
int meas_data_read(uint16_t index, meas_record_t* data);

/*
擦除指定索引位置的测量数据
参数：
  index: 数据索引
返回值：
  0: 成功
  -1: 索引超出范围
  -2: Flash操作失败
*/
int meas_data_erase(uint16_t index);

/*
擦除所有测量数据
返回值：
  0: 成功
  -1: Flash操作失败
*/
int meas_data_erase_all(void);

/*
清除外部Flash中用于历史记录的区域（仅500条记录范围，不影响其他区域）
返回值：
  0: 成功
  -1: 失败（预留，目前内部擦除如失败会触发复位检查）
*/
int meas_history_erase_all(void);

/*
获取有效数据的数量
返回值：
  有效数据的数量
*/
uint16_t meas_data_get_count(void);

/*
查找下一个空闲的存储位置
返回值：
  下一个空闲位置的索引 (1-500)
  0: 没有空闲位置
*/
uint16_t meas_data_find_next_free(void);

/*
循环写入测量数据（自动覆盖最旧数据）
参数：
  data: 要写入的测量数据指针
返回值：
  写入位置的索引
  -1: 写入失败
*/
int meas_data_write_cyclic(const meas_record_t* data);

/*
获取最旧数据的索引
返回值：
  最旧数据的索引
  0: 没有数据
*/
uint16_t meas_data_get_oldest_index(void);

/*
获取最新数据的索引
返回值：
  最新数据的索引
  0: 没有数据
*/
uint16_t meas_data_get_newest_index(void);

/*
获取存储状态信息
参数：
  total_records: 总记录数
  valid_records: 有效记录数
  oldest_index: 最旧记录索引
  newest_index: 最新记录索引
  is_full: 是否已满
返回值：
  0: 成功
  -1: 失败
*/
int meas_data_get_status(uint16_t* total_records, uint16_t* valid_records, 
                        uint16_t* oldest_index, uint16_t* newest_index, bool* is_full);


//循环存储
void save_data(uint32_t valid, uint32_t batt, uint32_t dark, uint32_t b, uint32_t g, uint32_t temp, uint32_t r, uint32_t stamp);
//获取测量数据
meas_record_t get_measure_data(void);


/*
按顺序读取外部Flash中全部历史记录，并通过 frame_send_string 逐条发送。
每条以：
"~~~DARK\tGREEN\tBLUE\tRESULT\tSTAMP@@@" 的格式输出，RESULT为mg/dL（小数两位）。
*/
void ext_record_send_all_history(void);

//锁状态和时间戳读写
int ext_stamp_write(uint32_t stamp);
int ext_stamp_read(uint32_t *stamp);
int ext_lock_write(uint32_t is_lock);
int ext_lock_read(uint32_t *is_lock);

//sn号读写
int ext_sn_write(const uint8_t* sn);
int ext_sn_read(uint8_t* sn);

//自动上锁读写
int ext_autolock_write(uint8_t is_autolock);
int ext_autolock_read(uint8_t* is_autolock);

// 比率参数读写API
int ratio_write(uint8_t ratio);
int ratio_read(uint8_t* ratio);

// PWM参数读写API
int pwm_write(uint8_t gpwm, uint8_t bpwm);
int pwm_read(uint8_t* gpwm, uint8_t* bpwm);

// 校准参数读写API
int cali_para_write(const cali_para_t* cali_data);
int cali_para_read(cali_para_t* cali_data);
bool cali_para_is_valid(const cali_para_t* cali_data);

// 错误日志存储API
/*
写入错误日志记录（循环覆盖）
参数：
  error_code: 错误代码
  timestamp: 时间戳
返回值：
  >= 0: 写入位置的索引 (0-511)
  -1: 写入失败
*/
int error_log_write(uint32_t error_code, uint32_t timestamp);

/*
读取指定索引的错误日志记录
参数：
  index: 记录索引 (0-511)
  record: 用于存储读取数据的缓冲区指针
返回值：
  0: 成功
  -1: 索引超出范围
  -2: 参数无效
  -3: 记录无效
*/
int error_log_read(uint16_t index, error_log_record_t* record);

/*
获取错误日志记录总数
返回值：
  有效记录的数量 (0-512)
*/
uint16_t error_log_get_count(void);

/*
获取最新的错误日志记录
参数：
  record: 用于存储读取数据的缓冲区指针
返回值：
  0: 成功
  -1: 参数无效
  -2: 没有有效记录
*/
int error_log_get_latest(error_log_record_t* record);

/*
获取最旧的错误日志记录
参数：
  record: 用于存储读取数据的缓冲区指针
返回值：
  0: 成功
  -1: 参数无效
  -2: 没有有效记录
*/
int error_log_get_oldest(error_log_record_t* record);

/*
清除所有错误日志记录
返回值：
  0: 成功
  -1: 失败
*/
int error_log_clear_all(void);

/*
发送所有错误日志记录到上位机
通过frame_send_string逐条发送错误日志
格式: "ERROR_CODE:timestamp"
*/
void error_log_send_all_history(void);

// SWDOE状态数据读写API
/*
写入SWDOE状态数据
参数：
  value: 要写入的SWDOE状态值 (0或1)
返回值：
  0: 成功
  -1: 写入失败
*/
int swdoe_state_write(uint8_t value);

/*
读取SWDOE状态数据
参数：
  value: 用于存储读取数据的指针
返回值：
  0: 成功
  -1: 读取失败
  -2: 参数无效
*/
int swdoe_state_read(uint8_t* value);

#endif
