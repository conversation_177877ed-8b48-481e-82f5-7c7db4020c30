#include "spiflash20.h"
#include "frame.h"
#include "nrf_nvic.h"
#include <string.h> 
//指令表

#define W25X_WriteEnable		  	0x06
#define W25X_WriteDisable		  	0x04
#define W25X_ReadStatusReg		  0x05
#define W25X_WriteStatusReg		  0x01
#define W25X_ReadData			  		0x03
#define W25X_FastReadData		  	0x0B
#define W25X_FastReadDual		  	0x3B
#define W25X_PageProgram		  	0x02
#define W25X_BlockErase			  	0xD8
#define W25X_SectorErase		  	0x20
#define W25X_ChipErase			  	0xC7
#define W25X_PowerDown			  	0xB9
#define W25X_ReleasePowerDown	  0xAB
#define W25X_DeviceID			  		0xAB
#define W25X_ManufactDeviceID	  0x90
#define W25X_JedecDeviceID		  0x9F   //9F    
#define FLASH_ID 0XBA6012  //器件ID
#define Dummy_Byte 0XFF
#define WIP_Flag  0x01


//芯片信息
#define FLASHPAGESIZE (256)
#define FALSHSECTORSIZE (4096)
#define FLASHBLOCKSIZE  (64*1024)

#define USER_SPI_CONFIG_IRQ_PRIORITY_HIGH 3

#define USRE_NRF_DRV_SPI_FLSH_CONFIG                         \
{                                                            \
    .sck_pin      = SPI_SCK_PIN,                            \
    .mosi_pin     = SPI_MOSI_PIN,                           \
    .miso_pin     = SPI_MISO_PIN,                           \
    .ss_pin       = NRF_DRV_SPI_PIN_NOT_USED,                \
    .irq_priority = USER_SPI_CONFIG_IRQ_PRIORITY_HIGH,         \
    .orc          = 0xFF,                                    \
    .frequency    = NRF_DRV_SPI_FREQ_8M,                   \
    .mode         = NRF_DRV_SPI_MODE_0,                      \
    .bit_order    = NRF_DRV_SPI_BIT_ORDER_MSB_FIRST,         \
}
#define SPI_BUFSIZE 16 //SPI缓存的大小

meas_record_t MT;

static const nrf_drv_spi_t spi = NRF_DRV_SPI_INSTANCE(2);  /**< SPI instance. */
static volatile bool spi_xfer_done;  /**< Flag used to indicate that SPI instance completed the transfer. */
volatile  uint8_t   SPIReadLength, SPIWriteLength;
uint8_t   SPI_Tx_Buf[SPI_BUFSIZE];  //发送
uint8_t   SPI_Rx_Buf[SPI_BUFSIZE];  //接收

void spi_event_handler(nrf_drv_spi_evt_t const * p_event, void *p_context)
{
    spi_xfer_done = true;
}

void spi_master_init(void)
{
    nrf_drv_spi_config_t spi_config = USRE_NRF_DRV_SPI_FLSH_CONFIG;
    spi_config.ss_pin   = NRF_DRV_SPI_PIN_NOT_USED;
    APP_ERROR_CHECK(nrf_drv_spi_init(&spi, &spi_config, spi_event_handler, NULL));
	
	nrf_gpio_cfg_output(SPI_SS_PIN);
}




/*
向flash中写入数据
参数  data 要写入的数据
*/
void spi_flash_write_reg(uint8_t data)
{
    spi_xfer_done = false;
    SPIWriteLength = 1;
    SPIReadLength = 0;
    SPI_Tx_Buf[0] = data;
    APP_ERROR_CHECK(nrf_drv_spi_transfer(&spi, SPI_Tx_Buf, SPIWriteLength, NULL, SPIReadLength));
    while(spi_xfer_done == false);
}
/*
从flash中读取数据
参数： reg 寄存器地址
*/
uint8_t spi_flash_read_reg(uint8_t reg)
{
    spi_xfer_done = false;
    SPI_Tx_Buf[0] = reg;
    APP_ERROR_CHECK(nrf_drv_spi_transfer(&spi, SPI_Tx_Buf, 0, SPI_Rx_Buf,1));
    while(spi_xfer_done == false);
    return SPI_Rx_Buf[0];
}

/*
读取flash的器件ID
*/
uint32_t spi_flash_ReadID(void)
{
    uint32_t temp = 0,temp0 = 0,temp1 = 0,temp2 = 0;
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_JedecDeviceID);
    temp0 = spi_flash_read_reg(0XFF);
    temp1 = spi_flash_read_reg(0XFF);
    temp2 = spi_flash_read_reg(0XFF);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    temp = (temp0 << 16)| (temp1 << 8) | temp2;
    return temp;
}

/*
写使能命令
*/
void spi_flash_WriteEnable(void)
{
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_WriteEnable);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选有效
}

/*
通过读状态寄存器等待FLASH芯片空闲
*/
void spi_flash_WaitForWriteEnd(void)
{
    unsigned char FLASH_Status = 0;
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ReadStatusReg); //发送读状态寄存器
    do {
        FLASH_Status = spi_flash_read_reg(Dummy_Byte);
    } while((WIP_Flag & FLASH_Status) == 1);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效

}
/*
擦除FLASH的扇区
参数 SectorAddr 要擦除的扇区地址
*/
void spi_flash_FLASH_SectorErase(uint32_t SectorAddr)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    spi_flash_WaitForWriteEnd();  //等待写完成
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_SectorErase);  //发送扇区擦除指令
    spi_flash_write_reg((SectorAddr & 0XFF0000) >> 16); //发送扇区擦除地址的高位
    spi_flash_write_reg((SectorAddr & 0XFF00) >> 8);
    spi_flash_write_reg(SectorAddr & 0XFF);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待擦除完成

    uint8_t check_buf[4];
    spi_flash_Flash_BufferRead(check_buf, SectorAddr, 4);
    if((check_buf[0] != 0xFF) || (check_buf[1] != 0xFF) ||
       (check_buf[2] != 0xFF) || (check_buf[3] != 0xFF)) {
        // NRF_LOG_ERROR("Sector erase failed!");
        NRF_LOG_INFO("Reset");
        nrf_delay_ms(100);
        NVIC_SystemReset();
    }
}

/*
FLASH页写入指令
参数：
备注：使用页写入指令最多可以一次向FLASH传输256个字节的数据
*/
void spi_flash_FLASH_PageWrite(unsigned char* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_PageProgram);  //发送写指令
    spi_flash_write_reg((WriteAddr & 0XFF0000) >> 16); //发送写地址的高位
    spi_flash_write_reg((WriteAddr & 0XFF00) >> 8);
    spi_flash_write_reg(WriteAddr & 0XFF);
    if(NumByteToWrite > 256) {
        NRF_LOG_INFO("write too large!\r\n");
        NRF_LOG_PROCESS();
        return ;
    }
    while(NumByteToWrite--) {
        spi_flash_write_reg(*pBuffer);
        pBuffer++;
    }
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待写完成
}
/*
从FLASH中读取数据
*/
void spi_flash_Flash_BufferRead(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead)
{
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ReadData);  //发送写指令
    spi_flash_write_reg((ReadAddr & 0XFF0000) >> 16); //发送写地址的高位
    spi_flash_write_reg((ReadAddr & 0XFF00) >> 8);
    spi_flash_write_reg(ReadAddr & 0XFF);
    while(NumByteToRead--) {
        *pBuffer = spi_flash_read_reg(Dummy_Byte);
        pBuffer++;
    }
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
}
/*
全片擦除
*/
void spi_flash_Chip_Erase(void)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ChipErase);  //全片擦除
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待写完成
}


void spi_flash_init(void)
{
    uint32_t ID = 0;
	spi_master_init();
    ID = spi_flash_ReadID();
    NRF_LOG_INFO("flash ID %d\r\n",ID);
    if(ID != FLASH_ID)
    {
        NRF_LOG_INFO("init w25q80 error\r\n");

    }
    else
    {
        NRF_LOG_INFO("init w25q80 ok!\r\n");
        NRF_LOG_INFO("FLASH ID is %X",ID);
    }
}

// ==================== 测量数据存储API实现 ====================

/*
计算指定索引对应的Flash地址
*/
static uint32_t meas_data_get_address(uint16_t index)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        return 0;  // 无效索引
    }
    return MEAS_DATA_START_ADDR + (index - 1) * MEAS_RECORD_SIZE;
}

/*
检查数据是否有效
*/
static bool meas_data_is_valid(const meas_record_t* data)
{
    return (data->is_valid == 0x12345678);  // 使用魔数标识有效数据
}

/*
写入测量数据到指定索引位置
*/
int meas_data_write(uint16_t index, const meas_record_t* data)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        NRF_LOG_ERROR("meas_data_write: Invalid index %d", index);
        return -1;  // 索引超出范围
    }

    if (data == NULL) {
        NRF_LOG_ERROR("meas_data_write: NULL data pointer");
        return -2;  // 无效参数
    }

    uint32_t flash_addr = meas_data_get_address(index);
    uint32_t sector_addr = (flash_addr / 4096) * 4096;  // 计算扇区地址

    // 读取整个扇区的数据
    uint8_t sector_buffer[4096];
    spi_flash_Flash_BufferRead(sector_buffer, sector_addr, 4096);

    // 修改对应位置的数据
    uint32_t offset_in_sector = flash_addr - sector_addr;
    memcpy(&sector_buffer[offset_in_sector], data, MEAS_RECORD_SIZE);

    // 擦除扇区
    spi_flash_FLASH_SectorErase(sector_addr);

    // 写回整个扇区
    for (uint32_t i = 0; i < 4096; i += 256) {
        uint16_t write_size = (4096 - i) > 256 ? 256 : (4096 - i);
        spi_flash_FLASH_PageWrite(&sector_buffer[i], sector_addr + i, write_size);
    }

    NRF_LOG_INFO("meas_data_write: Written data to index %d", index);
    return 0;  // 成功
}

/*
从指定索引位置读取测量数据
*/
int meas_data_read(uint16_t index, meas_record_t* data)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        NRF_LOG_ERROR("meas_data_read: Invalid index %d", index);
        return -1;  // 索引超出范围
    }

    if (data == NULL) {
        NRF_LOG_ERROR("meas_data_read: NULL data pointer");
        return -2;  // 无效参数
    }

    uint32_t flash_addr = meas_data_get_address(index);

    // 从Flash读取数据
    spi_flash_Flash_BufferRead((uint8_t*)data, flash_addr, MEAS_RECORD_SIZE);

    // 检查数据有效性
    if (!meas_data_is_valid(data)) {
        NRF_LOG_WARNING("meas_data_read: Invalid data at index %d", index);
        memset(data, 0, MEAS_RECORD_SIZE);  // 清空无效数据
        return -3;  // 数据无效
    }

    NRF_LOG_INFO("meas_data_read: Read data from index %d", index);
    return 0;  // 成功
}

/*
擦除指定索引位置的测量数据
*/
int meas_data_erase(uint16_t index)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        NRF_LOG_ERROR("meas_data_erase: Invalid index %d", index);
        return -1;  // 索引超出范围
    }

    uint32_t flash_addr = meas_data_get_address(index);
    uint32_t sector_addr = (flash_addr / 4096) * 4096;  // 计算扇区地址

    // 读取整个扇区的数据
    uint8_t sector_buffer[4096];
    spi_flash_Flash_BufferRead(sector_buffer, sector_addr, 4096);

    // 清空对应位置的数据
    uint32_t offset_in_sector = flash_addr - sector_addr;
    memset(&sector_buffer[offset_in_sector], 0xFF, MEAS_RECORD_SIZE);

    // 擦除扇区
    spi_flash_FLASH_SectorErase(sector_addr);

    // 写回整个扇区
    for (uint32_t i = 0; i < 4096; i += 256) {
        uint16_t write_size = (4096 - i) > 256 ? 256 : (4096 - i);
        spi_flash_FLASH_PageWrite(&sector_buffer[i], sector_addr + i, write_size);
    }

    NRF_LOG_INFO("meas_data_erase: Erased data at index %d", index);
    return 0;  // 成功
}

/*
擦除所有测量数据
*/
int meas_data_erase_all(void)
{
    // 计算需要擦除的扇区数量
    uint32_t total_size = MAX_MEAS_RECORDS * MEAS_RECORD_SIZE;
    uint32_t sectors_needed = (total_size + 4095) / 4096;  // 向上取整

    // 擦除所有相关扇区
    for (uint32_t i = 0; i < sectors_needed; i++) {
        uint32_t sector_addr = MEAS_DATA_START_ADDR + (i * 4096);
        spi_flash_FLASH_SectorErase(sector_addr);
    }
    return 0;  // 成功
}

/*
清除外部Flash中用于历史记录的区域（仅500条记录范围，不影响其他区域）
*/
int meas_history_erase_all(void)
{
    uint32_t total_size = MAX_MEAS_RECORDS * MEAS_RECORD_SIZE;
    uint32_t sectors_needed = (total_size + 4095) / 4096;  // 向上取整

    for (uint32_t i = 0; i < sectors_needed; i++) {
        uint32_t sector_addr = MEAS_DATA_START_ADDR + (i * 4096);
        spi_flash_FLASH_SectorErase(sector_addr);
    }

    return 0;
}

/*
获取有效数据的数量
*/
uint16_t meas_data_get_count(void)
{
    uint16_t count = 0;
    meas_record_t temp_data;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (meas_data_is_valid(&temp_data)) {
            count++;
        }
    }

    NRF_LOG_INFO("meas_data_get_count: Found %d valid records", count);
    return count;
}

/*
查找下一个空闲的存储位置
*/
uint16_t meas_data_find_next_free(void)
{
    meas_record_t temp_data;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (!meas_data_is_valid(&temp_data)) {
            NRF_LOG_INFO("meas_data_find_next_free: Found free slot at index %d", i);
            return i;
        }
    }

    NRF_LOG_WARNING("meas_data_find_next_free: No free slots available");
    return 0;  // 没有空闲位置
}

// ==================== 循环覆盖功能实现 ====================

/*
获取最旧数据的索引
*/
uint16_t meas_data_get_oldest_index(void)
{
    meas_record_t temp_data;
    uint16_t oldest_index = 0;
    uint32_t oldest_stamp = 0xFFFFFFFF;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (meas_data_is_valid(&temp_data) && temp_data.stamp < oldest_stamp) {
            oldest_stamp = temp_data.stamp;
            oldest_index = i;
        }
    }

    NRF_LOG_INFO("meas_data_get_oldest_index: Oldest record at index %d, stamp %lu", oldest_index, oldest_stamp);
    return oldest_index;
}

/*
获取最新数据的索引
*/
uint16_t meas_data_get_newest_index(void)
{
    meas_record_t temp_data;
    uint16_t newest_index = 0;
    uint32_t newest_stamp = 0;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (meas_data_is_valid(&temp_data) && temp_data.stamp > newest_stamp) {
            newest_stamp = temp_data.stamp;
            newest_index = i;
        }
    }

    NRF_LOG_INFO("meas_data_get_newest_index: Newest record at index %d, stamp %lu", newest_index, newest_stamp);
    return newest_index;
}

/*
循环写入测量数据（自动覆盖最旧数据）
*/
int meas_data_write_cyclic(const meas_record_t* data)
{
    if (data == NULL) {
        NRF_LOG_ERROR("meas_data_write_cyclic: NULL data pointer");
        return -1;
    }

    // 查找下一个空闲位置
    uint16_t next_free = meas_data_find_next_free();
    
    if (next_free > 0) {
        // 有空闲位置，直接写入
        NRF_LOG_INFO("meas_data_write_cyclic: Writing to free slot %d", next_free);
        return meas_data_write(next_free, data);
    } else {
        // 没有空闲位置，覆盖最旧的数据
        uint16_t oldest_index = meas_data_get_oldest_index();
        
        if (oldest_index == 0) {
			oldest_index = 1;
            NRF_LOG_INFO("meas_data_write_cyclic: oldest_index==0, fallback to index 1");
        }

        NRF_LOG_INFO("meas_data_write_cyclic: Overwriting oldest record at index %d", oldest_index);
        
        // 覆盖最旧的数据
        int result = meas_data_write(oldest_index, data);
        if (result == 0) {
            NRF_LOG_INFO("meas_data_write_cyclic: Successfully overwrote record at index %d", oldest_index);
        }
        return result;
    }
}

/*
获取存储状态信息
*/
int meas_data_get_status(uint16_t* total_records, uint16_t* valid_records, 
                        uint16_t* oldest_index, uint16_t* newest_index, bool* is_full)
{
    if (total_records == NULL || valid_records == NULL || 
        oldest_index == NULL || newest_index == NULL || is_full == NULL) {
        NRF_LOG_ERROR("meas_data_get_status: NULL pointer parameter");
        return -1;
    }

    *total_records = MAX_MEAS_RECORDS;
    *valid_records = meas_data_get_count();
    *oldest_index = meas_data_get_oldest_index();
    *newest_index = meas_data_get_newest_index();
    *is_full = (*valid_records >= MAX_MEAS_RECORDS);

    NRF_LOG_INFO("meas_data_get_status: Total=%d, Valid=%d, Oldest=%d, Newest=%d, Full=%s", 
                 *total_records, *valid_records, *oldest_index, *newest_index, 
                 *is_full ? "Yes" : "No");

    return 0;
}

//存储数据到flash
void save_data(uint32_t valid, uint32_t batt, uint32_t dark, uint32_t b, uint32_t g, uint32_t temp, uint32_t r, uint32_t stamp){
	MT.is_valid = valid;
	MT.raw_data_batt = batt;
	MT.raw_data_dark = dark;
	MT.raw_data_b10 = b;
	MT.raw_data_g100 = g;
	MT.temperature = temp;
	MT.result = r;
	MT.stamp = stamp;
}

//获取测量数据
meas_record_t get_measure_data(void){
	return MT;
}

/*
按顺序读取外部Flash中全部历史记录，并通过 frame_send_string 逐条发送。
格式参考 FDS 的 record_send_all_history：
先发一条标题，随后每条为：
  "~~~DARK\tGREEN\tBLUE\tRESULT\tSTAMP@@@"
其中 RESULT 为 mg/dL，小数两位（内部存储为*1000）。
*/
void ext_record_send_all_history(void)
{
    frame_send_string("order: DARK, GREEN, BLUE, RESULT, IDX");

    char line[100];
    uint16_t sent = 0; // 已发送的有效记录序号（从1开始）
    meas_record_t rec;
    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t addr = MEAS_DATA_START_ADDR + (i - 1) * MEAS_RECORD_SIZE;
        spi_flash_Flash_BufferRead((uint8_t*)&rec, addr, MEAS_RECORD_SIZE);
        if (rec.is_valid == 0x12345678) {
            float result_mgdl = (float)rec.result / 1000.0f;
            sent++;
            snprintf(line, sizeof(line), "~~~%d\t%d\t%d\t%0.2f\t%d\tIDX=%u@@@",
                     rec.raw_data_dark, rec.raw_data_g100, rec.raw_data_b10, result_mgdl, rec.stamp, sent);
            frame_send_string(line);
        }
    }
}

// ========== 元数据（时间戳/锁状态） ==========
// 时间戳写入/读取
int ext_stamp_write(uint32_t stamp)
{
    spi_flash_FLASH_SectorErase(EXT_STAMP_SECTOR_ADDR);
    spi_flash_FLASH_PageWrite((unsigned char*)&stamp, EXT_STAMP_SECTOR_ADDR, sizeof(uint32_t));
    return 0;
}

int ext_stamp_read(uint32_t *stamp)
{
    if (stamp == NULL) return -1;
    uint32_t tmp = 0xFFFFFFFF;
    spi_flash_Flash_BufferRead((uint8_t*)&tmp, EXT_STAMP_SECTOR_ADDR, sizeof(uint32_t));
    *stamp = tmp;
    return 0;
}

// 锁状态写入/读取
int ext_lock_write(uint32_t is_lock)
{
    spi_flash_FLASH_SectorErase(EXT_LOCK_SECTOR_ADDR);
    spi_flash_FLASH_PageWrite((unsigned char*)&is_lock, EXT_LOCK_SECTOR_ADDR, sizeof(uint32_t));
    return 0;
}

int ext_lock_read(uint32_t *is_lock)
{
    if (is_lock == NULL) return -1;
    uint32_t tmp = 0xFFFFFFFF;
    spi_flash_Flash_BufferRead((uint8_t*)&tmp, EXT_LOCK_SECTOR_ADDR, sizeof(uint32_t));
    *is_lock = tmp;
    return 0;
}

// 序列号写入
int ext_sn_write(const uint8_t* sn)
{
    if (sn == NULL) return -1;
    
    // 擦除元数据扇区
    spi_flash_FLASH_SectorErase(EXT_META_SECTOR_ADDR);
    
    // 写入序列号到扇区开始位置
    spi_flash_FLASH_PageWrite((unsigned char*)sn, EXT_META_SECTOR_ADDR, 20);
    
    return 0;
}

// 序列号读取
int ext_sn_read(uint8_t* sn)
{
    if (sn == NULL) return -1;
    
    // 从元数据扇区读取序列号
    spi_flash_Flash_BufferRead(sn, EXT_META_SECTOR_ADDR, 20);
    
    return 0;
}

//自动上锁写入
int ext_autolock_write(uint8_t is_autolock)
{
    // 擦除自动锁状态扇区
    spi_flash_FLASH_SectorErase(EXT_AUTOLOCK_SECTOR_ADDR);
    
    // 写入自动锁状态到扇区开始位置
    spi_flash_FLASH_PageWrite((unsigned char*)&is_autolock, EXT_AUTOLOCK_SECTOR_ADDR, sizeof(uint8_t));
    
    return 0;
}

//自动上锁读取
int ext_autolock_read(uint8_t* is_autolock)
{
    if (is_autolock == NULL) return -1;
    
    // 从自动锁状态扇区读取数据
    spi_flash_Flash_BufferRead(is_autolock, EXT_AUTOLOCK_SECTOR_ADDR, sizeof(uint8_t));
    
    return 0;
}

// ========== 比率参数存储API ==========

int ratio_write(uint8_t ratio)
{
    // 擦除比率参数扇区
    spi_flash_FLASH_SectorErase(RATIO_SECTOR_ADDR);
    
    // 写入比率参数到扇区开始位置
    spi_flash_FLASH_PageWrite((unsigned char*)&ratio, RATIO_SECTOR_ADDR, sizeof(uint8_t));
    
    return 0;
}

int ratio_read(uint8_t* ratio)
{
    if (ratio == NULL) return -1;
    
    // 从比率参数扇区读取数据
    spi_flash_Flash_BufferRead(ratio, RATIO_SECTOR_ADDR, sizeof(uint8_t));
    
    return 0;
}

// ========== PWM参数存储API==========
int pwm_write(uint8_t gpwm, uint8_t bpwm)
{
    // 创建PWM参数结构体
    uint8_t pwm_data[2];
    pwm_data[0] = gpwm;
    pwm_data[1] = bpwm;
    
    // 擦除PWM参数扇区
    spi_flash_FLASH_SectorErase(PWM_SECTOR_ADDR);
    
    // 写入PWM参数到扇区开始位置
    spi_flash_FLASH_PageWrite((unsigned char*)pwm_data, PWM_SECTOR_ADDR, sizeof(pwm_data));
    
    return 0;
}

int pwm_read(uint8_t* gpwm, uint8_t* bpwm)
{
    if (gpwm == NULL || bpwm == NULL) return -1;
    
    // 创建PWM参数缓冲区
    uint8_t pwm_data[2];
    
    // 从PWM参数扇区读取数据
    spi_flash_Flash_BufferRead(pwm_data, PWM_SECTOR_ADDR, sizeof(pwm_data));
    
    // 解析数据
    *gpwm = pwm_data[0];
    *bpwm = pwm_data[1];
    
    return 0;
}

// ========== 校准参数存储API==========
int cali_para_write(const cali_para_t* cali_data)
{
    if (cali_data == NULL) return -1;
    
    // 擦除校准参数扇区
    spi_flash_FLASH_SectorErase(CALI_PARA_SECTOR_ADDR);
    
    // 写入校准参数到扇区开始位置
    spi_flash_FLASH_PageWrite((unsigned char*)cali_data, CALI_PARA_SECTOR_ADDR, sizeof(cali_para_t));
    
    return 0;
}

int cali_para_read(cali_para_t* cali_data)
{
    if (cali_data == NULL) return -1;
    
    // 从校准参数扇区读取数据
    spi_flash_Flash_BufferRead((uint8_t*)cali_data, CALI_PARA_SECTOR_ADDR, sizeof(cali_para_t));
    
    return 0;
}

//预留
bool cali_para_is_valid(const cali_para_t* cali_data)
{
    if (cali_data == NULL) return false;
    
    // 检查is_valid字段，0表示未校准，1-3表示已校准
    if (cali_data->is_valid == 0) return false;
    
    // 检查gotkb字段，0x55表示k和b参数存在
    if (cali_data->gotkb != 0x55) return false;
    
    // 检查result_ratio字段，应该在合理范围内（比如50-200）
    if (cali_data->result_ratio < 50 || cali_data->result_ratio > 200) return false;
    
    // 检查PWM值是否在合理范围内（0-255）
    if (cali_data->g_pwm > 255 || cali_data->b_pwm > 255) return false;
    
    return true;
}

// ========== 错误日志存储API实现 ==========
/*
计算指定索引对应的Flash地址
*/
static uint32_t error_log_get_address(uint16_t index)
{
    if (index >= MAX_ERROR_LOG_RECORDS) {
        return 0;  // 无效索引
    }
    return ERROR_LOG_SECTOR_ADDR + (index * ERROR_LOG_RECORD_SIZE);
}

/*
检查错误日志记录是否有效
*/
static bool error_log_is_valid(const error_log_record_t* record)
{
    // 检查错误代码和时间戳是否为有效值（非0xFFFFFFFF）
    return (record->error_code != 0xFFFFFFFF && record->timestamp != 0xFFFFFFFF);
}

/*
查找下一个写入位置（循环覆盖）
*/
static uint16_t error_log_find_next_write_index(void)
{
    static uint16_t next_index = 0;  // 静态变量保持下次写入位置
    
    // 简单的循环索引
    uint16_t current_index = next_index;
    next_index = (next_index + 1) % MAX_ERROR_LOG_RECORDS;
    
    return current_index;
}

/*
写入错误日志记录（循环覆盖）
*/
int error_log_write(uint32_t error_code, uint32_t timestamp)
{
    // 创建错误日志记录
    error_log_record_t record;
    record.error_code = error_code;
    record.timestamp = timestamp;
    
    // 获取写入位置
    uint16_t write_index = error_log_find_next_write_index();
    uint32_t flash_addr = error_log_get_address(write_index);
    
    if (flash_addr == 0) {
        NRF_LOG_ERROR("error_log_write: Invalid write index %d", write_index);
        return -1;
    }
    
    // 读取整个扇区的数据
    uint8_t sector_buffer[4096];
    spi_flash_Flash_BufferRead(sector_buffer, ERROR_LOG_SECTOR_ADDR, 4096);
    
    // 修改对应位置的数据
    uint32_t offset_in_sector = flash_addr - ERROR_LOG_SECTOR_ADDR;
    memcpy(&sector_buffer[offset_in_sector], &record, ERROR_LOG_RECORD_SIZE);
    
    // 擦除扇区
    spi_flash_FLASH_SectorErase(ERROR_LOG_SECTOR_ADDR);
    
    // 写回整个扇区
    for (uint32_t i = 0; i < 4096; i += 256) {
        uint16_t write_size = (4096 - i) > 256 ? 256 : (4096 - i);
        spi_flash_FLASH_PageWrite(&sector_buffer[i], ERROR_LOG_SECTOR_ADDR + i, write_size);
    }
    
    NRF_LOG_INFO("error_log_write: Written error_code=0x%08X, timestamp=%u to index %d", 
                 error_code, timestamp, write_index);
    
    return write_index;  // 返回写入位置的索引
}

/*
读取指定索引的错误日志记录
*/
int error_log_read(uint16_t index, error_log_record_t* record)
{
    if (index >= MAX_ERROR_LOG_RECORDS) {
        NRF_LOG_ERROR("error_log_read: Invalid index %d", index);
        return -1;  // 索引超出范围
    }
    
    if (record == NULL) {
        NRF_LOG_ERROR("error_log_read: NULL record pointer");
        return -2;  // 参数无效
    }
    
    uint32_t flash_addr = error_log_get_address(index);
    
    // 从Flash读取数据
    spi_flash_Flash_BufferRead((uint8_t*)record, flash_addr, ERROR_LOG_RECORD_SIZE);
    
    // 检查记录有效性
    if (!error_log_is_valid(record)) {
        NRF_LOG_WARNING("error_log_read: Invalid record at index %d", index);
        return -3;  // 记录无效
    }
    
    NRF_LOG_INFO("error_log_read: Read error_code=0x%08X, timestamp=%u from index %d", 
                 record->error_code, record->timestamp, index);
    
    return 0;  // 成功
}

/*
获取错误日志记录总数
*/
uint16_t error_log_get_count(void)
{
    uint16_t count = 0;
    error_log_record_t temp_record;
    
    for (uint16_t i = 0; i < MAX_ERROR_LOG_RECORDS; i++) {
        uint32_t flash_addr = error_log_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_record, flash_addr, ERROR_LOG_RECORD_SIZE);
        
        if (error_log_is_valid(&temp_record)) {
            count++;
        }
    }
    
    NRF_LOG_INFO("error_log_get_count: Found %d valid error records", count);
    return count;
}

/*
获取最新的错误日志记录
*/
int error_log_get_latest(error_log_record_t* record)
{
    if (record == NULL) {
        NRF_LOG_ERROR("error_log_get_latest: NULL record pointer");
        return -1;  // 参数无效
    }
    
    error_log_record_t temp_record;
    uint32_t latest_timestamp = 0;
    bool found = false;
    
    for (uint16_t i = 0; i < MAX_ERROR_LOG_RECORDS; i++) {
        uint32_t flash_addr = error_log_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_record, flash_addr, ERROR_LOG_RECORD_SIZE);
        
        if (error_log_is_valid(&temp_record) && temp_record.timestamp > latest_timestamp) {
            latest_timestamp = temp_record.timestamp;
            *record = temp_record;
            found = true;
        }
    }
    
    if (!found) {
        NRF_LOG_WARNING("error_log_get_latest: No valid error records found");
        return -2;  // 没有有效记录
    }
    
    NRF_LOG_INFO("error_log_get_latest: Latest error_code=0x%08X, timestamp=%u", 
                 record->error_code, record->timestamp);
    
    return 0;  // 成功
}

/*
获取最旧的错误日志记录
*/
int error_log_get_oldest(error_log_record_t* record)
{
    if (record == NULL) {
        NRF_LOG_ERROR("error_log_get_oldest: NULL record pointer");
        return -1;  // 参数无效
    }
    
    error_log_record_t temp_record;
    uint32_t oldest_timestamp = 0xFFFFFFFF;
    bool found = false;
    
    for (uint16_t i = 0; i < MAX_ERROR_LOG_RECORDS; i++) {
        uint32_t flash_addr = error_log_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_record, flash_addr, ERROR_LOG_RECORD_SIZE);
        
        if (error_log_is_valid(&temp_record) && temp_record.timestamp < oldest_timestamp) {
            oldest_timestamp = temp_record.timestamp;
            *record = temp_record;
            found = true;
        }
    }
    
    if (!found) {
        NRF_LOG_WARNING("error_log_get_oldest: No valid error records found");
        return -2;  // 没有有效记录
    }
    
    NRF_LOG_INFO("error_log_get_oldest: Oldest error_code=0x%08X, timestamp=%u", 
                 record->error_code, record->timestamp);
    
    return 0;  // 成功
}

/*
清除所有错误日志记录
*/
int error_log_clear_all(void)
{
    // 擦除错误日志扇区
    spi_flash_FLASH_SectorErase(ERROR_LOG_SECTOR_ADDR);
    
    NRF_LOG_INFO("error_log_clear_all: All error log records cleared");
    
    return 0;  // 成功
}

/*
发送所有错误日志记录到上位机
*/
void error_log_send_all_history(void)
{
    frame_send_string("Error Log History: ERROR_CODE:TIMESTAMP");
    
    char line[64];
    uint16_t sent = 0;
    error_log_record_t record;
    
    for (uint16_t i = 0; i < MAX_ERROR_LOG_RECORDS; i++) {
        uint32_t flash_addr = error_log_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&record, flash_addr, ERROR_LOG_RECORD_SIZE);
        
        if (error_log_is_valid(&record)) {
            sent++;
            snprintf(line, sizeof(line), "ERROR_%03d: 0x%08X:%u", 
                     sent, record.error_code, record.timestamp);
            frame_send_string(line);
        }
    }
    
    if (sent == 0) {
        frame_send_string("No error records found");
    } else {
        snprintf(line, sizeof(line), "Total error records: %d", sent);
        frame_send_string(line);
    }
}

// ========== SWDOE状态数据存储API实现 ==========

/*
写入SWDOE状态数据
*/
int swdoe_state_write(uint8_t value)
{
    // 擦除SWDOE状态数据扇区
    spi_flash_FLASH_SectorErase(SWDOE_STATE_SECTOR_ADDR);

    // 写入uint8_t数据到扇区开始位置
    spi_flash_FLASH_PageWrite(&value, SWDOE_STATE_SECTOR_ADDR, SWDOE_STATE_DATA_SIZE);

    return 0;
}

/*
读取SWDOE状态数据
*/
int swdoe_state_read(uint8_t* value)
{
    if (value == NULL) return -2;

    // 从SWDOE状态数据扇区读取uint8_t数据
    spi_flash_Flash_BufferRead(value, SWDOE_STATE_SECTOR_ADDR, SWDOE_STATE_DATA_SIZE);

    return 0;
}