#ifndef __LED_ARRAY_H__
#define __LED_ARRAY_H__


#include "main.h"




typedef void (*page_timer_timeout_handler_t)(void);



void led_array_init(void);
void led_array_deinit(void);
void page_start_timer(uint32_t interval_ms, page_timer_timeout_handler_t handler);
void page_stop_timer(void);
void led_array_set_bright(uint32_t bright);
void led_array_update(void);
void led_array_copy(uint32_t* data);
void led_array_clear(void);
void led_array_point(uint32_t x, uint32_t y, uint32_t color);
void led_array_rectangle(uint32_t x1, uint32_t y1, uint32_t x2, uint32_t y2, uint32_t color);
void led_array_hor_line(uint32_t x1, uint32_t x2, uint32_t y, uint32_t color);
void led_array_ver_line(uint32_t x, uint32_t y1, uint32_t y2, uint32_t color);
void led_array_digit(uint32_t x, uint32_t digit, uint32_t color);


extern app_timer_id_t m_page_timer;


#endif
