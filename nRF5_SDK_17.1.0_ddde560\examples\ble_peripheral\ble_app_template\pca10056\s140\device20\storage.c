#include "storage.h"
#include "frame.h"
#include "algo.h"
#include "bm8563.h"
#include "spiflash20.h"
/*
File ID可用范围：0x0000 - 0xBFFF
Record Key可用范围：0x0001 - 0xBFFF

update或者delete之前必须先find，fds_record_update或者fds_record_delete会用到参数descriptor，这个descriptor必须是通过fds_record_find返回

所以FDS内部维护了record id，该字段就是每个记录再flash中的唯一标识，由FDS模块内部维护。不需要开发者操作。
当删除几个file id和record key都相同的记录时，需要开发者自己调用函数 枚举找到这几个记录，虽然每次查找时传入的file id 和record key都一样，但是fds返回的记录描述符中的record id是不同的。
再依次使用该返回的描述符就可以正确删除这些同样file id和record key的记录了。

*/

//#define MAX_RECORDS  1100
//#define RECORD_KEY_MIN 0x1001
//#define RECORD_KEY_MAX 0x1444
#define MAX_RECORDS   5
#define RECORD_KEY_MIN 0x1001
#define RECORD_KEY_MAX 0x1007

static volatile bool m_fds_initialized = false;//初始化完成标志
static volatile bool m_fds_working = false;//忙标志
static store_t m_store;
static cali_para_t gcali_para;

//RECORD相关
static void record_send_timeout_handler(void * p_context);
APP_TIMER_DEF(record_send_timer_id);        //用于间隔发送记录的定时器
//static volatile uint32_t is_record_sending = 0;	//是否正在发送标志
static fds_record_desc_t   record_record_desc;
static fds_find_token_t    record_ftok;
static fds_flash_record_t  record_flash_record;

static temprecord gtemp_data;

/* Array to map FDS events to strings. */
static char const * fds_evt_str[] = {
    "FDS_EVT_INIT",
    "FDS_EVT_WRITE",
    "FDS_EVT_UPDATE",
    "FDS_EVT_DEL_RECORD",
    "FDS_EVT_DEL_FILE",
    "FDS_EVT_GC",
};

/* Keep track of the progress of a delete_all operation. */
static struct {
    bool delete_next;   //!< Delete next record.
    bool pending;       //!< Waiting for an fds FDS_EVT_DEL_RECORD event, to delete the next record.
} m_delete_all;

const char *fds_err_str(ret_code_t err_code)
{
    /* Array to map FDS return values to strings. */
    static char const * err_str[] = {
        "FDS_ERR_OPERATION_TIMEOUT",
        "FDS_ERR_NOT_INITIALIZED",
        "FDS_ERR_UNALIGNED_ADDR",
        "FDS_ERR_INVALID_ARG",
        "FDS_ERR_NULL_ARG",
        "FDS_ERR_NO_OPEN_RECORDS",
        "FDS_ERR_NO_SPACE_IN_FLASH",
        "FDS_ERR_NO_SPACE_IN_QUEUES",
        "FDS_ERR_RECORD_TOO_LARGE",
        "FDS_ERR_NOT_FOUND",
        "FDS_ERR_NO_PAGES",
        "FDS_ERR_USER_LIMIT_REACHED",
        "FDS_ERR_CRC_CHECK_FAILED",
        "FDS_ERR_BUSY",
        "FDS_ERR_INTERNAL",
    };

    return err_str[err_code - NRF_ERROR_FDS_ERR_BASE];
}


// Simple event handler to handle errors during initialization.
static void fds_evt_handler(fds_evt_t const * p_evt)
{
    if (p_evt->result == NRF_SUCCESS) {
        //NRF_LOG_INFO("Event: %s received (NRF_SUCCESS)", fds_evt_str[p_evt->id]);
    } else {
        NRF_LOG_WARNING("Event: %s received (%s)", fds_evt_str[p_evt->id], fds_err_str(p_evt->result));
    }

    switch (p_evt->id) {
    case FDS_EVT_INIT:
        if (p_evt->result == NRF_SUCCESS) {
            m_fds_initialized = true;
        }
        break;

    case FDS_EVT_WRITE:
    case FDS_EVT_UPDATE:
    case FDS_EVT_DEL_RECORD:
        if (p_evt->result == NRF_SUCCESS) {
            //NRF_LOG_INFO("Record ID:\t0x%04x",  p_evt->write.record_id);
            //NRF_LOG_INFO("File ID:\t0x%04x",    p_evt->write.file_id);
            //NRF_LOG_INFO("Record key:\t0x%04x", p_evt->write.record_key);
            m_fds_working = false;
            //碎片收集整理
            ret_code_t err_code = fds_gc();
            APP_ERROR_CHECK(err_code);
        }
        break;

    default:
        break;
    }
}

bool record_delete_next(void)
{
    fds_find_token_t  tok   = {0};
    fds_record_desc_t desc  = {0};

    if (fds_record_iterate(&desc, &tok) == NRF_SUCCESS) {
        ret_code_t rc = fds_record_delete(&desc);
        if (rc != NRF_SUCCESS) {
            return false;
        }
        return true;
    } else {
        /* No records left to delete. */
        return false;
    }
}
/**@brief   Begin deleting all records, one by one. */
void delete_all_begin(void)
{
    m_delete_all.delete_next = true;

}


/**@brief   Process a delete all command.
 *
 * Delete records, one by one, until no records are left.
 */
static uint32_t del_repeatcnt = 0;
void delete_all_process(void)
{

    if (m_delete_all.delete_next
        & !m_delete_all.pending) {
//        NRF_LOG_INFO("Deleting next record.");
        m_delete_all.delete_next = record_delete_next();
        if (!m_delete_all.delete_next) {
            if(del_repeatcnt++<5) {
                m_delete_all.delete_next = true;
                nrf_delay_ms(350);
            } else {
                del_repeatcnt = 0;
                m_delete_all.delete_next = false;
                frame_send_string("del record OK");
            }
//				  NRF_LOG_INFO("No records left to delete.");
        }
    }


}

//新建记录
void fds_new(uint16_t file, uint16_t key, uint8_t* data, uint32_t len)
{
    APP_ERROR_CHECK_BOOL(file <= 0xBFFF);
    APP_ERROR_CHECK_BOOL(1 <= key && key <= 0xBFFF);

    ret_code_t err_code;
    fds_record_t record;
    fds_record_desc_t record_desc;

    record.file_id           = file;
    record.key               = key;
    record.data.p_data       = data;
    record.data.length_words = (len + 3) / 4;

//    while (!m_fds_initialized)
//	{
//        sd_app_evt_wait();	// 等待过程中待机
//    };

    m_fds_working = true;
    err_code = fds_record_write(&record_desc, &record);

    if(err_code == FDS_ERR_NO_SPACE_IN_FLASH) {
        NRF_LOG_INFO("recode full");
//	  NRF_LOG_PROCESS();
        record_delete_next();
        delete_all_begin();
        fds_record_write(&record_desc, &record);
    } else {
        APP_ERROR_CHECK(err_code);
    }
//	err_code = fds_record_write(&record_desc, &record);
//	if (err_code != FDS_ERR_NO_SPACE_IN_FLASH) APP_ERROR_CHECK(err_code);
}


//更新记录
static void fds_update(uint16_t file, uint16_t key, uint8_t* data, uint32_t len)
{
    APP_ERROR_CHECK_BOOL(file <= 0xBFFF);
    APP_ERROR_CHECK_BOOL(1 <= key && key <= 0xBFFF);

    ret_code_t err_code;
    fds_record_t        record;
    fds_record_desc_t   record_desc;
    fds_find_token_t    ftok;

    record.file_id           = file;
    record.key               = key;
    record.data.p_data       = data;
    record.data.length_words = (len + 3) / 4;

//    while (!m_fds_initialized)
//	{
//        sd_app_evt_wait();	// 等待过程中待机
//    };

    memset(&ftok, 0x00, sizeof(fds_find_token_t));//It is required to zero the token before first use.
    err_code = fds_record_find(file, key, &record_desc, &ftok);
    if (err_code == NRF_SUCCESS) { //数据存在
        m_fds_working = true;
        err_code = fds_record_update(&record_desc, &record);
        APP_ERROR_CHECK(err_code);

    } else {
        APP_ERROR_CHECK_BOOL(false);
    }

}


//读取记录
static bool fds_read(uint16_t file, uint16_t key, uint8_t* data, uint32_t len)
{
    APP_ERROR_CHECK_BOOL(file <= 0xBFFF);
    APP_ERROR_CHECK_BOOL(1 <= key && key <= 0xBFFF);

    ret_code_t err_code;
    fds_flash_record_t  flash_record;
    fds_record_desc_t   record_desc;
    fds_find_token_t    ftok;

    while (!m_fds_initialized) {
        sd_app_evt_wait();	// 等待过程中待机
    };

    memset(&ftok, 0x00, sizeof(fds_find_token_t));//It is required to zero the token before first use.
    err_code = fds_record_find(file, key, &record_desc, &ftok);
    if (err_code == NRF_SUCCESS) { //数据存在
        err_code = fds_record_open(&record_desc, &flash_record);
        APP_ERROR_CHECK(err_code);
        memcpy(data, flash_record.p_data, len);
        err_code = fds_record_close(&record_desc);
        APP_ERROR_CHECK(err_code);

        return true;
    } else { //如果没有这条数据，直接断言通知应用层
        return false;
    }
}


//FDS初始化
void storage_init(void)
{
    ret_code_t err_code;

    //初始化，会与peer_manager重复，但没有副作用
    err_code = fds_register(fds_evt_handler);
    APP_ERROR_CHECK(err_code);
    err_code = fds_init();
    APP_ERROR_CHECK(err_code);
    while (!m_fds_initialized) {
        sd_app_evt_wait();	// 等待过程中待机
    };

    //统计
    static fds_stat_t stat = {0};
    err_code = fds_stat(&stat);
    APP_ERROR_CHECK(err_code);

    //初始化数据
    if (fds_read(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t)) == false) {
        memset(&m_store, 0, sizeof(store_t));
        m_store.is_locked = 0;    //divid  -----
        fds_new(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
    }

    //碎片收集整理
    err_code = fds_gc();
    APP_ERROR_CHECK(err_code);
}


//读取指定的记录
//index: 0 ~ (MEAS_RECORD_NUM-1)
void storage_read_record(uint32_t index, meas_record_t *meas_record)
{
    APP_ERROR_CHECK_BOOL(index <= MEAS_RECORD_NUM - 1);

    memcpy((uint8_t*)meas_record, (uint8_t*)&m_store.meas_record[index], sizeof(meas_record_t));
}


//更新一条记录
void storage_update_record(meas_record_t *meas_record)
{
    for (uint32_t i = MEAS_RECORD_NUM - 1; i > 0; i--) {
        m_store.meas_record[i] = m_store.meas_record[i - 1];
    }
    m_store.meas_record[0] = *meas_record;

    fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}


//读取校准参数
void storage_read_cali_para(cali_para_t *cali_para)
{
    spi_flash_Flash_BufferRead((uint8_t*)cali_para,00,sizeof(cali_para_t));
    memcpy((uint8_t*)&gcali_para,(uint8_t*)cali_para,sizeof(cali_para_t));
//    NRF_LOG_INFO("spi flash Rd");
//		NRF_LOG_PROCESS();
//   memcpy((uint8_t*)cali_para, (uint8_t*)&m_store.cali_para, sizeof(cali_para_t));  //divid  ---
}


//更新校准参数
void storage_update_cali_para(cali_para_t *cali_para)
{
    spi_flash_FLASH_PageWrite((uint8_t*)cali_para,00,sizeof(cali_para_t));
//	  NRF_LOG_INFO("spi flash Wr");
//		NRF_LOG_PROCESS();
    /*
    m_store.cali_para = *cali_para;
    fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
    */
}


//读取产测结果
void storage_read_factory_test(factory_test_t *factory_test)
{
    memcpy((uint8_t*)factory_test, (uint8_t*)&m_store.factory_test, sizeof(factory_test_t));
}


//更新产测结果
void storage_update_factory_test(factory_test_t *factory_test)
{
    m_store.factory_test = *factory_test;

    fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}


//读取加锁状态
void storage_read_lock(uint32_t *is_locked)
{
    memcpy((uint32_t*)is_locked, (uint32_t*)&m_store.is_locked, sizeof(uint32_t));
}


//更新加锁状态
void storage_update_lock(uint32_t *is_locked)
{
    m_store.is_locked = *is_locked;

    fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}

//divid add 获取是否得到k b
void storage_read_kb_status(uint32_t *kb_status)
{
    memcpy((uint32_t*)kb_status, (uint32_t*)&gcali_para.gotkb, sizeof(uint32_t));
}

//更新是否得到k b的状态
void storage_update_kb_status(uint32_t *kb_status)
{
    gcali_para.gotkb = *kb_status;
//   fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}

//divid +++++++++++++++++++++++++++++++++
//获取k值的符号
void storage_read_k_sign(uint32_t *ksign)
{
    memcpy((uint32_t*)ksign, (uint32_t*)&gcali_para.ksign, sizeof(uint32_t));
}
//更新k值的符号
void storage_update_k_sign(uint32_t *ksign)
{
    gcali_para.ksign = *ksign;
    //fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}
//获取b值的符号
void storage_read_b_sign(uint32_t *bsign)
{
    memcpy((uint32_t*)bsign, (uint32_t*)&gcali_para.bsign, sizeof(uint32_t));
}
//更新b值的符号
void storage_update_b_sign(uint32_t *k)
{
    gcali_para.bsign = *k;
    // fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}
void storage_update_stamp(uint32_t stamp)
{
    gcali_para.stamp = stamp;
    storage_update_cali_para(&gcali_para);
//		fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
//		NRF_LOG_INFO("set preset stamp:%d",stamp);
//		NRF_LOG_PROCESS();
}
void storage_update_tempture(uint8_t th,uint8_t tl)
{
    gcali_para.temph = th;
    gcali_para.templ = tl;
}
void storage_update_duty(uint32_t gpwm,uint32_t bpwm)
{
    gcali_para.g_pwm = (uint8_t)gpwm;
    gcali_para.b_pwm = (uint8_t)bpwm;
}

/*
void storage_clear_kbsign(void)
{
    m_store.cali_para.gotkb = 0x00;
    fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}
*/


//获取k值 真实k值乘以10000，然后取整
void storage_read_k(uint32_t *k)
{
    memcpy((uint32_t*)k, (uint32_t*)&gcali_para.k, sizeof(uint32_t));
}
//更新k值
void storage_update_k(uint32_t *k)
{
    gcali_para.k = *k;
    //  fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}

//获取b值 真实b值乘以10000，然后取整
void storage_read_b(uint32_t *b)
{
    memcpy((uint32_t*)b, (uint32_t*)&gcali_para.b, sizeof(uint32_t));
}
//更新b值
void storage_update_b(uint32_t *b)
{
    gcali_para.b = *b;
    spi_flash_FLASH_PageWrite((uint8_t*) &gcali_para,00,sizeof(cali_para_t));
//	  NRF_LOG_INFO("spi flash Wr");
//		NRF_LOG_PROCESS();
//   fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}

void storage_read_stamp(uint32_t *stamp)
{
//	*stamp = m_store.cali_para.stamp;

    memcpy((uint32_t*)stamp, (uint32_t*)&gcali_para.stamp, sizeof(uint32_t));
    NRF_LOG_INFO("stamp: %d",*stamp);
    NRF_LOG_PROCESS();
}

void storage_read_unit(uint32_t *uint)
{
    memcpy((uint32_t*)uint, (uint32_t*)&gcali_para.uint, sizeof(uint32_t));
//	  NRF_LOG_INFO("uint flash Rd");
//		NRF_LOG_PROCESS();
}
void storage_updata_unit(uint32_t *uint)
{
    gcali_para.uint = *uint;
    spi_flash_FLASH_PageWrite((uint8_t*) &gcali_para,00,sizeof(cali_para_t));

}
void storage_read_tempture(uint8_t *th,uint8_t *tl)
{
    memcpy((uint8_t*)th, (uint8_t*)&gcali_para.temph, sizeof(uint8_t));
    memcpy((uint8_t*)tl, (uint8_t*)&gcali_para.templ, sizeof(uint8_t));
}
void storage_read_duty(uint32_t *gduty,uint32_t *bduty)
{
    memcpy((uint8_t*)gduty, (uint8_t*)&gcali_para.g_pwm, sizeof(uint8_t));
    memcpy((uint8_t*)bduty, (uint8_t*)&gcali_para.b_pwm, sizeof(uint8_t));
}




//--------------------------------------------------------------

//读取序列号字符串，长度20B
void storage_read_sn_str(uint8_t *str)
{
    memcpy((uint8_t*)str, (uint8_t*)&m_store.sn_str, 20);
}


//更新序列号字符串，长度20B
void storage_update_sn_str(uint8_t *str)
{
    memcpy((uint8_t*)&m_store.sn_str, (uint8_t*)str, 20);

    fds_update(CALI_USER_FILE_ID, CALI_USER_KEY, (uint8_t*)&m_store, sizeof(store_t));
}

void record_init(void)
{
    ret_code_t ret_code;
    // fds_stat_t stat = {0};

    // // 存储空间校验
    // fds_stat(&stat);
    // if ((stat.freeable_words * 4) < (MAX_RECORDS * 8)) {
    //     NRF_LOG_ERROR("Insufficient FDS space!");
    // }

    ret_code = app_timer_create(&record_send_timer_id, APP_TIMER_MODE_REPEATED, record_send_timeout_handler);
    APP_ERROR_CHECK(ret_code);

}

void record_add_meas_record(meas_record_t *meas_record)
{
    static uint16_t current_key = RECORD_KEY_MIN;
    // ret_code_t err_code;

    // 查找并删除最旧记录（当超过容量时）
    if (current_key > RECORD_KEY_MAX) {
        fds_record_desc_t  oldest_desc;
        fds_find_token_t   ftok = {0};
        uint16_t oldest_key = RECORD_KEY_MAX;

        // 遍历查找最小键值记录
        while (fds_record_find(RECORD_FILE_ID, RECORD_KEY, &oldest_desc, &ftok) == NRF_SUCCESS) {
            fds_flash_record_t record = {0};
            if (fds_record_open(&oldest_desc, &record) == NRF_SUCCESS) {
                if (record.p_header->record_key < oldest_key) {
                    oldest_key = record.p_header->record_key;
                }
                fds_record_close(&oldest_desc);
            }
        }
        // 删除找到的最旧记录
        if (oldest_key != RECORD_KEY_MAX) {
            fds_record_delete(&oldest_desc);
        }
        current_key = RECORD_KEY_MIN; // 重置键值
    }

    // 添加新记录（带递增键值）
    // err_code = fds_new(RECORD_FILE_ID, current_key++, (uint8_t*)meas_record, sizeof(meas_record_t));

    // if (err_code == FDS_ERR_NO_SPACE_IN_FLASH)
    // {
    //     fds_gc();
    //     fds_new(RECORD_FILE_ID, current_key++, (uint8_t*)meas_record, sizeof(meas_record_t));
    // }
    // fds_new(RECORD_FILE_ID, RECORD_KEY, (uint8_t*)meas_record, sizeof(meas_record_t));
    fds_new(RECORD_FILE_ID, current_key++, (uint8_t*)meas_record, sizeof(meas_record_t));
}


//定时调用，间隔50ms
static void record_send_timeout_handler(void * p_context)
{
    ret_code_t ret_code;

    // 迭代查找所有file id和record key相同的记录
    //ret_code = fds_record_find(RECORD_FILE_ID, RECORD_KEY, &record_record_desc, &record_ftok);
    ret_code = fds_record_iterate(&record_record_desc, &record_ftok);
    // ret_code = fds_record_find(RECORD_FILE_ID, FDS_RECORD_KEY_DIRTY, &record_record_desc, &record_ftok);
    if (ret_code == NRF_SUCCESS) {	//存在数据
        if (fds_record_open(&record_record_desc, &record_flash_record) == NRF_SUCCESS) {
            //打开成功，访问数据，flash_record保存了记录的相关信息和内容。
            meas_record_t meas_record;
            memcpy((uint8_t*)&meas_record, record_flash_record.p_data, sizeof(meas_record));
            //meas_record.result += 50;//四舍五入显示//printf已经四舍五入了

            char str[100];
            snprintf(str, sizeof(str), "~~~%d\t%d\t%d\t%0.2f\t%d@@@", meas_record.raw_data_dark, meas_record.raw_data_g100, meas_record.raw_data_b10, (float)meas_record.result / 1000.0f,meas_record.stamp);
            frame_send_string(str);

            //访问结束关闭记录
            ret_code = fds_record_close(&record_record_desc);
            APP_ERROR_CHECK(ret_code);
        }
    } else {	//结束遍历
        NRF_LOG_INFO("send done");
        ret_code = app_timer_stop(record_send_timer_id);
        APP_ERROR_CHECK(ret_code);
        m_fds_working = 0;
    }
}
void record_send_all_history(void)
{
//	NRF_LOG_INFO("record_send_all_history");

    ret_code_t ret_code;

    //不能重复发送
    if (m_fds_working) {
        return;
    }
    m_fds_working = 1;

    algo_init();
    char str[100];
    snprintf(str, sizeof(str), "order: DARK, GREEN, BLUE, RESULT");
    frame_send_string(str);

    //开始遍历
    memset(&record_ftok, 0x00, sizeof(fds_find_token_t));

    //开启定时间隔发送
    ret_code = app_timer_start(record_send_timer_id, APP_TIMER_TICKS(50), NULL);
    APP_ERROR_CHECK(ret_code);
}

//保存记录
void record_save(uint32_t dark, uint32_t green, uint32_t blue, uint32_t bilirubin)
{
    //保存为触摸按键调用可查看
    static meas_record_t meas_record;
    meas_record.is_valid = 1;
    meas_record.raw_data_batt = 0;
    meas_record.raw_data_dark = dark;
    meas_record.raw_data_b10 = blue;
    meas_record.raw_data_g100 = green;
    meas_record.result = bilirubin;
    meas_record.stamp = GetTimeStamp();

    //	storage_update_record(&meas_record);	//divid --   不需要缓存的20条记录
    //保存原始数据，医院采样用
    record_add_meas_record(&meas_record);
}

void get_temp_data(temprecord *ptemp_data)
{
    memcpy(ptemp_data,&gtemp_data,sizeof(temprecord));
}
void set_temp_data(temprecord *ptemp_data)
{
    memcpy(&gtemp_data,ptemp_data,sizeof(temprecord));
}
void get_temp_flash_data(void)
{
    spi_flash_Flash_BufferRead((uint8_t*)&gtemp_data,0x400,sizeof(temprecord));
}
void set_temp_flash_data(void)
{
    spi_flash_FLASH_PageWrite((uint8_t*)&gtemp_data,0x400,sizeof(temprecord));
}
