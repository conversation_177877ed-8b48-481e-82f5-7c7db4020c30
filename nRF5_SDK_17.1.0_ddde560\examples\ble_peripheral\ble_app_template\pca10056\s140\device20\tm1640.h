#ifndef __TM1640_H__
#define __TM1640_H__


#include "main.h"


void tm1640_wrbyte(unsigned char oneByte); //?????

void tm1640_brighteness(unsigned char x);  // 0--8 grade

void tm1640_disp_close(void);  // close disp

void tm1640_disp_on(void);  // close disp


void tm1640_clear(void);


//======================
void tm1640_show_ready(void); //?????? ????


void tm1640_show_bye(void); //?????? ????


void tm1640_show_lock(void); //?????? ????

void tm1640_show_Result_1(unsigned char ten,unsigned char one,unsigned char f1);  //1  fraction
void tm1640_show_Result_2(unsigned char ten,unsigned char one,unsigned char f1, unsigned char f2);
void tm1640_show_Result_3(unsigned char bai,unsigned char ten,unsigned char one,unsigned char f1);

//static void page_result_show2(uint32_t result, uint32_t point_num);
void tm1640_show_char(unsigned char addr,unsigned val); //?????? ????
void tm1640_show_unit(unsigned int unit);
void tm1640_show_arrow(unsigned char val);
void tm1640_init(unsigned char bri);
void tm1640_show_nc(void);
void tm1640_show_n_3(unsigned char val);
void tm1640_show_E01(void);
void tm1640_show_Batt_empty(void);
void tm1640_show_mg_dl(void);
void tm1640_show_bmp_umol(void);
void tm1640_show_dot(unsigned char val);
void tm1640_show_n_5(unsigned char val);
void tm1640_show_cal_1(void);
void tm1640_show_cal_2(void);
void tm1640_show_cal_ok(void);
#endif
