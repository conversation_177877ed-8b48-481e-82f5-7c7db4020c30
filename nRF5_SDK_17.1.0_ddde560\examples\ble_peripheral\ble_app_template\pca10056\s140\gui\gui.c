#include "gui.h"
#include "lv_port_disp.h"
#include "lvgl.h"
#include "key.h"
#include "gui_guider.h"
#include "sm_measure_mode.h"
#include "sm_measure.h"
#include "sm_measure_unit.h"
#include "bm8563.h"

//https://docs.lvgl.io/master/examples.html

APP_TIMER_DEF(lvgl_timer_id);

static lv_ui ui;

void set_current_gui(ui_screen_t ui);
ui_screen_t get_current_gui(void);

static void lvgl_timeout_handler(void * p_context);

extern uint32_t bilirubin_average; // 外部测量结果变量
extern float average_measurement_results[3];

volatile uint32_t start_up_ms = 0;
volatile uint32_t debug_ms = 0;

//主界面渐变线和圆
static lv_color_t canvas_buf[GRADIENT_WIDTH * CANVAS_HEIGHT];
static lv_obj_t *canvas;
static lv_color_t bg_buf[GRADIENT_WIDTH * CANVAS_HEIGHT];  // 背景备份

static lv_obj_t *line_objs[LINE_COUNT];

// 平均模式下的界面元素
static lv_obj_t *average_date_line_objs[AVERAGE_DATE_LINE_COUNT];  // 日期位置的6根细下划线
static lv_obj_t *average_bottom_line_objs[LINE_COUNT];             // 底部的粗下划线
static lv_obj_t *average_measurement_labels[3] = {NULL, NULL, NULL}; // 平均模式下的三次测量结果标签

//电量计
static lv_obj_t *battery_bar;    // 电量条
static lv_obj_t *battery_label;  // 百分比文字（可选）

//上升/下降指示
static volatile bool s_line_animating = false; // 指示条动画锁：仅一条动画
static line_anim_ctx_t line_ctx[3]; // 三条线
static uint8_t current_line = 0;
static lv_obj_t * g_parent = NULL;
#if LV_USE_LOG
//LVGL打印函数
static void lv_log_print_g_cb(const char * buf)
{

}
#endif

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    get_ui
  * @brief
  * @param   None
  * @retval
  * <AUTHOR>
  * @Data    2025-07-21
  * 1. ...
  * <Function>:get ui
 **/
/* -------------------------------- end -------------------------------- */

lv_ui get_ui(void)
{
    return ui;
}

//10ms定时器回调
static void lvgl_timeout_handler(void * p_context)
{
    //LVGL心跳
    lv_tick_inc(1);
    if (start_up_ms <= 150 ) {
        start_up_ms += 1;
        if (start_up_ms == 150) {
            //打开背光
            nrf_gpio_pin_write(GPIO_LCD_BK, 1);
        }
    }
}



//GUI初始化
void gui_init(void)
{
    lv_init();

    lv_port_disp_init();

    // 初始化平均模式测量标签数组
    for (int i = 0; i < 3; i++) {
        average_measurement_labels[i] = NULL;
    }

    ret_code_t ret_code = 0;
    //1ms定时器
    ret_code = app_timer_create(&lvgl_timer_id, APP_TIMER_MODE_REPEATED, lvgl_timeout_handler);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(lvgl_timer_id, APP_TIMER_TICKS(1), NULL);
    APP_ERROR_CHECK(ret_code);

}


void switch_to_next_screen(ui_screen_t current_screen)
{
    clear_average_measurement_labels();

    lv_obj_t *old_scr = lv_scr_act();
    if (old_scr) {
        lv_obj_del(old_scr);
    }
    switch (current_screen) {
    case UI_SCREEN_LOG:
        setup_scr_log(&ui);
        lv_scr_load(ui.log);
        break;
    case UI_SCREEN_BABY2S:
        setup_scr_baby2s(&ui);
        lv_scr_load(ui.baby2s);
        break;
    case UI_SCREEN_BLUEDIS:
        setup_scr_blueDis(&ui);
        lv_scr_load(ui.blueDis);
        break;
    case UI_SCREEN_BLUETOOTHCONNECT:
        setup_scr_bluetoothConnect(&ui);
        lv_scr_load(ui.bluetoothConnect);
        break;
    case UI_SCREEN_CHARGED:
        setup_scr_charged(&ui);
        lv_scr_load(ui.charged);
        break;
    case UI_SCREEN_LOCK:
        setup_scr_lock(&ui);
        lv_scr_load(ui.lock);
        break;
    case UI_SCREEN_LOWBATTERY:
        setup_scr_lowBattery(&ui);
        lv_scr_load(ui.lowBattery);
        break;
    case UI_SCREEN_RESULT1:
        setup_scr_result1(&ui);
        lv_scr_load(ui.result1);
        break;
    case UI_SCREEN_SINGLE:
        setup_scr_single(&ui);
        lv_scr_load(ui.single);
        break;
    case UI_SCREEN_AVERAGE:
        setup_scr_average(&ui);
        lv_scr_load(ui.average);
        break;
    case UI_SCREEN_MEASURE:
        setup_scr_measure(&ui);
        lv_scr_load(ui.measure);
        break;
    case UI_SCREEN_HISTORY:
        setup_scr_history(&ui);
        lv_scr_load(ui.history);
        break;
	case UI_SCREEN_CALIBRATE:
		setup_scr_calibrate(&ui);
		lv_scr_load(ui.calibrate);
		break;
	case UI_SCREEN_ERROR:
		setup_scr_error(&ui);
		lv_scr_load(ui.error);
		break;
    default:
        break;
    }
}

//画渐变线
void gradient_bar_init(lv_obj_t *parent)
{
    //需要先创建canvas对象
    canvas = lv_canvas_create(parent);
    lv_canvas_set_buffer(canvas, canvas_buf, GRADIENT_WIDTH, CANVAS_HEIGHT, LV_IMG_CF_TRUE_COLOR);
    lv_obj_align(canvas, LV_ALIGN_BOTTOM_MID, 0, -10);

    // 填充背景透明
    lv_canvas_fill_bg(canvas, lv_color_black(), LV_OPA_TRANSP);

    // 计算线条起始 y 坐标（使其从圆中心穿过）
    int line_y_start = CANVAS_HEIGHT / 2 - GRADIENT_HEIGHT / 2;

    // 绘制渐变线
    for (int x = 0; x < GRADIENT_WIDTH; x++) {
        uint8_t r = (uint32_t)x * 255 / (GRADIENT_WIDTH - 1);
        uint8_t g = 255 - r;
        lv_color_t color = lv_color_make(r, g, 0);
        for (int y = line_y_start; y < line_y_start + GRADIENT_HEIGHT; y++) {
            lv_canvas_set_px(canvas, x, y, color);
        }
    }
}

//画自适应圆
void draw_indicator_on_canvas(uint8_t percent)
{
    if (percent < 1) percent = 1;
    if (percent > 100) percent = 100;

    int x = (GRADIENT_WIDTH - 1) * (percent - 1) / 99;
    int cx = x;
    int cy = CANVAS_HEIGHT / 2;  // 圆心y = canvas中间

    // 获取当前点颜色
    lv_color_t color = lv_canvas_get_px(canvas, x, cy);

    // 重新绘制渐变线（如果频繁刷新，可优化为只第一次绘制）
    lv_canvas_fill_bg(canvas, lv_color_black(), LV_OPA_TRANSP);

    int line_y_start = CANVAS_HEIGHT / 2 - GRADIENT_HEIGHT / 2;
    for (int px = 0; px < GRADIENT_WIDTH; px++) {
        uint8_t r = (uint32_t)px * 255 / (GRADIENT_WIDTH - 1);
        uint8_t g = 255 - r;
        lv_color_t c = lv_color_make(r, g, 0);
        for (int py = line_y_start; py < line_y_start + GRADIENT_HEIGHT; py++) {
            lv_canvas_set_px(canvas, px, py, c);
        }
    }

    // 画圆（直径16）
    int radius = INDICATOR_RADIUS;
    for (int py = cy - radius; py <= cy + radius; py++) {
        for (int px = cx - radius; px <= cx + radius; px++) {
            int dx = px - cx;
            int dy = py - cy;
            if (dx * dx + dy * dy <= radius * radius) {
                lv_canvas_set_px(canvas, px, py, color);
            }
        }
    }
}


void create_empty_result_lines(lv_obj_t *parent)
{
    // 根据测量模式决定下划线的显示方式
    if(get_measure_mode() == AVERAGE_MODE) {
        // 平均模式：不显示传统的下划线，而是显示靠右的粗下划线
        int line_count = (get_measure_unit() == RESULT1_MODE) ? 3 : 2;  // μmol/L显示3条，mg/dL显示2条
        create_average_bottom_lines(parent, line_count);
    } else {
        // 单次模式：显示传统的居中下划线，数量根据测量单位调整
        int base_x = 50;
        int base_y = 120;
        int line_count = (get_measure_unit() == RESULT1_MODE) ? 3 : 2;  // μmol/L显示3条，mg/dL显示2条

        for (int i = 0; i < line_count; i++) {
            lv_obj_t *line = lv_obj_create(parent);
            line_objs[i] = line; // 保存指针
            lv_obj_set_size(line, LINE_WIDTH, LINE_HEIGHT);
            lv_obj_set_style_radius(line, LINE_HEIGHT / 2, LV_PART_MAIN);
            lv_obj_set_style_bg_color(line, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
            lv_obj_set_style_bg_opa(line, LV_OPA_COVER, LV_PART_MAIN);
            lv_obj_clear_flag(line, LV_OBJ_FLAG_SCROLLABLE);
            lv_obj_set_pos(line, base_x + i * (LINE_WIDTH + LINE_SPACING), base_y);
        }

        // 清空未使用的指针
        for (int i = line_count; i < LINE_COUNT; i++) {
            line_objs[i] = NULL;
        }
    }
}
void clear_result_lines(void)
{
    // 根据测量模式清除对应的下划线
    if(get_measure_mode() == AVERAGE_MODE) {
        // 平均模式：清除靠右的粗下划线
        clear_average_bottom_lines();
    } else {
        // 单次模式：清除传统的居中下划线
        for (int i = 0; i < LINE_COUNT; i++) {
            if (line_objs[i]) {
                lv_obj_del(line_objs[i]);
                line_objs[i] = NULL;
            }
        }
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    create_average_date_lines
  * @brief   在平均模式下创建日期位置的6根细下划线
  * @param   parent: 父对象
  * @retval  None
  * <AUTHOR>
  * @Data    2025-07-31
  * 1. 创建6根细下划线，两两为一组，共3组
  * 2. 每组内间隙较小，组间间隙较大
  * <Function>: Create 6 thin underlines for date position in average mode
 **/
/* -------------------------------- end -------------------------------- */
void create_average_date_lines(lv_obj_t *parent)
{
    int base_x = 24;  // 与日期标签相同的起始位置
    int base_y = 40;  // 与日期标签相同的Y位置

    for (int i = 0; i < AVERAGE_DATE_LINE_COUNT; i++) {
        lv_obj_t *line = lv_obj_create(parent);
        average_date_line_objs[i] = line; // 保存指针
        lv_obj_set_size(line, AVERAGE_DATE_LINE_WIDTH, AVERAGE_DATE_LINE_HEIGHT);
        lv_obj_set_style_radius(line, AVERAGE_DATE_LINE_HEIGHT / 2, LV_PART_MAIN);
        lv_obj_set_style_bg_color(line, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
        lv_obj_set_style_bg_opa(line, LV_OPA_COVER, LV_PART_MAIN);
        lv_obj_clear_flag(line, LV_OBJ_FLAG_SCROLLABLE);

        // 计算位置：两两为一组，组内间隙小，组间间隙大
        int group = i / 2;  // 第几组（0, 1, 2）
        int pos_in_group = i % 2;  // 组内位置（0, 1）

        int x_offset = group * (2 * AVERAGE_DATE_LINE_WIDTH + AVERAGE_DATE_LINE_SPACING + AVERAGE_DATE_GROUP_SPACING) +
                       pos_in_group * (AVERAGE_DATE_LINE_WIDTH + AVERAGE_DATE_LINE_SPACING);

        lv_obj_set_pos(line, base_x + x_offset, base_y + 10); // Y位置稍微下移一点
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    clear_average_date_lines
  * @brief   清除平均模式下日期位置的细下划线
  * @param   None
  * @retval  None
  * <AUTHOR>
  * @Data    2025-07-31
  * <Function>: Clear thin underlines for date position in average mode
 **/
/* -------------------------------- end -------------------------------- */
void clear_average_date_lines(void)
{
    for (int i = 0; i < AVERAGE_DATE_LINE_COUNT; i++) {
        if (average_date_line_objs[i]) {
            lv_obj_del(average_date_line_objs[i]);
            average_date_line_objs[i] = NULL;
        }
    }
}
/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    clear_average_date_lines
  * @brief   清除平均模式下日期位置的细下划线(指定)
  * @param   index:下标
  * @retval  None
  * <AUTHOR>
  * @Data    2025-08-05
 **/
/* -------------------------------- end -------------------------------- */
void clear_average_index_date_line(int index)
{
    // 在平均值模式下，每组有两条线，需要清除对应组的两条线
    int group = index;  // 每个测量对应一个组
    int start_index = group * 2;  // 每组的起始索引
    int end_index = start_index + 1;  // 每组的结束索引

    // 确保索引在有效范围内
    if (start_index >= 0 && end_index < AVERAGE_DATE_LINE_COUNT) {
        // 清除对应组的两条线
        for(int i = start_index; i <= end_index; i++) {
            if (average_date_line_objs[i]) {
                lv_obj_del(average_date_line_objs[i]);
                average_date_line_objs[i] = NULL;
            }
        }
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    create_average_bottom_lines
  * @brief   在平均模式下创建底部的粗下划线
  * @param   parent: 父对象
  * @param   line_count: 下划线数量（根据测量单位调整）
  * @retval  None
  * <AUTHOR>
  * @Data    2025-07-31
  * 1. 创建底部粗下划线，位置靠右
  * 2. 与单位显示区域保持适当间隙
  * <Function>: Create thick underlines at bottom in average mode
 **/
/* -------------------------------- end -------------------------------- */
void create_average_bottom_lines(lv_obj_t *parent, int line_count)
{
    // 确保line_count不超过最大值
    if (line_count > LINE_COUNT) {
        line_count = LINE_COUNT;
    }

    int base_x = 95;  // 单位标签的X位置
    int base_y = 120;  // 与原来的下划线相同的Y位置

    for (int i = 0; i < line_count; i++) {
        lv_obj_t *line = lv_obj_create(parent);
        average_bottom_line_objs[i] = line; // 保存指针
        lv_obj_set_size(line, LINE_WIDTH, LINE_HEIGHT);
        lv_obj_set_style_radius(line, LINE_HEIGHT / 2, LV_PART_MAIN);
        lv_obj_set_style_bg_color(line, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
        lv_obj_set_style_bg_opa(line, LV_OPA_COVER, LV_PART_MAIN);
        lv_obj_clear_flag(line, LV_OBJ_FLAG_SCROLLABLE);

        int x_offset = i * (LINE_WIDTH + LINE_SPACING);
        lv_obj_set_pos(line, base_x + x_offset, base_y);
    }
    // 清空未使用的指针
    for (int i = line_count; i < LINE_COUNT; i++) {
        average_bottom_line_objs[i] = NULL;
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    clear_average_bottom_lines
  * @brief   清除平均模式下底部的粗下划线
  * @param   None
  * @retval  None
  * <AUTHOR>
  * @Data    2025-07-31
  * <Function>: Clear thick underlines at bottom in average mode
 **/
/* -------------------------------- end -------------------------------- */
void clear_average_bottom_lines(void)
{
    for (int i = 0; i < LINE_COUNT; i++) {
        if (average_bottom_line_objs[i]) {
            lv_obj_del(average_bottom_line_objs[i]);
            average_bottom_line_objs[i] = NULL;
        }
    }
}


/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    clear_average_measurement_labels
  * @brief   安全地清理平均模式下的测量结果标签，避免重叠显示
  * <AUTHOR>
  * @Data    2025-08-13
 **/
/* -------------------------------- end -------------------------------- */
static void clear_average_measurement_labels(void)
{
    for (int i = 0; i < 3; i++) {
        if (average_measurement_labels[i] != NULL) {
            // 检查标签是否仍然有效
            if (lv_obj_is_valid(average_measurement_labels[i])) {
                // 先隐藏标签，避免显示问题
                lv_obj_add_flag(average_measurement_labels[i], LV_OBJ_FLAG_HIDDEN);
                // 安全删除标签
                lv_obj_del(average_measurement_labels[i]);
            }
            // 无论如何都将指针设为NULL
            average_measurement_labels[i] = NULL;
        }
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    update_average_measurement_labels_unit
  * @brief   更新已存在的测量标签的单位显示，不删除标签
  * <AUTHOR>
  * @Data    2025-08-13
 **/
/* -------------------------------- end -------------------------------- */
static void update_average_measurement_labels_unit(void)
{
    extern float average_measurement_results[3];
    extern bool average_results_displayed[3];
    extern uint32_t average_times_now;

    char buf[16];

    // 检查是否在平均模式
    if (get_measure_mode() != AVERAGE_MODE) {
        return;
    }

    // 只更新已显示的标签的文本内容，不创建新标签
    for (int i = 0; i < average_times_now && i < 3; i++) {
        if (average_measurement_labels[i] != NULL && average_results_displayed[i]) {
            // 检查标签是否仍然有效
            if (lv_obj_is_valid(average_measurement_labels[i])) {
                memset(buf, 0, 16);

                // 根据当前单位格式化显示文本
                if(get_measure_unit() == RESULT1M_MODE) {
                    // Convert to mg/dL: μmol/L * 17.1 / 1000 = mg/dL
                    snprintf(buf, sizeof(buf), "%.2f", average_measurement_results[i] * 17.1f);
                } else {
                    // Display as μmol/L (original unit)
                    snprintf(buf, sizeof(buf), "%.2f", average_measurement_results[i]);
                }

                // 只更新文本，不重新创建标签
                lv_label_set_text(average_measurement_labels[i], buf);
            } else {
                // 如果标签无效，将指针设为NULL
                average_measurement_labels[i] = NULL;
            }
        }
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    show_average_measurement_result
  * @brief   在平均模式下显示单个测量结果
  * @param   measurement_index 测量序号（0-2）
  * @param   value 测量值
  * <AUTHOR>
  * @Data    2025-08-05
 **/
/* -------------------------------- end -------------------------------- */

void show_average_measurement_result(int measurement_index, float value)
{
    // 防止越界（3组：0、1、2）
    if (measurement_index < 0 || measurement_index >= 3) {
        NRF_LOG_WARNING("show_average_measurement_result: invalid measurement_index=%d", measurement_index);
        return;
    }

    if (get_measure_mode() != AVERAGE_MODE) {
        return;
    }

    int base_x = 25;
    int base_y = 40;

    // 获取外部的测量结果数组
    extern float average_measurement_results[3];

    char buf[16];

    // 显示从第0个到当前measurement_index的所有数据
    for(int i = 0; i <= measurement_index; i++) {
        memset(buf, 0, 16);

        // 如果标签不存在，创建新的标签
        if (average_measurement_labels[i] == NULL) {
            // 在平均模式下，标签应该创建在ui.average容器上
            lv_obj_t *parent_container = ui.average;
            if (parent_container == NULL) {
                NRF_LOG_ERROR("Average container not available, measurement_index=%d", i);
                continue;
            }

            average_measurement_labels[i] = lv_label_create(parent_container);
            if (average_measurement_labels[i] == NULL) {
                NRF_LOG_ERROR("Failed to create measurement label %d", i);
                continue;
            }

            // 根据测量单位调整标签大小和字体
            if(get_measure_unit() == RESULT1_MODE) {
                // umol/L单位
                lv_obj_set_size(average_measurement_labels[i], AVERAGE_DATE_LINE_WIDTH * 4, 18);
                lv_obj_set_style_text_font(average_measurement_labels[i], &lv_PingFang_regular_18, LV_PART_MAIN | LV_STATE_DEFAULT);
            } else {
                // mg/dL单位
                lv_obj_set_size(average_measurement_labels[i], AVERAGE_DATE_LINE_WIDTH * 4, 18);
                lv_obj_set_style_text_font(average_measurement_labels[i], &lv_PingFang_regular_18, LV_PART_MAIN | LV_STATE_DEFAULT);
            }
            
            lv_obj_set_style_text_color(average_measurement_labels[i], lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_align(average_measurement_labels[i], LV_TEXT_ALIGN_LEFT, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(average_measurement_labels[i], 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            
            // 根据测量单位调整位置
            int label_x = base_x + i * (2 * AVERAGE_DATE_LINE_WIDTH + AVERAGE_DATE_LINE_SPACING + AVERAGE_DATE_GROUP_SPACING);
            if(get_measure_unit() == RESULT1_MODE) {
                // umol/L单位：左移位置
                label_x -= 8;
            }
            lv_obj_set_pos(average_measurement_labels[i], label_x, base_y);
        }

        // 根据当前单位格式化显示文本
        if(get_measure_unit() == RESULT1M_MODE) {
            // Convert to mg/dL: μmol/L * 17.1 / 1000 = mg/dL
            snprintf(buf, sizeof(buf), "%.2f", average_measurement_results[i] * 17.1f);
        } else {
            // Display as μmol/L (original unit)
            snprintf(buf, sizeof(buf), "%.2f", average_measurement_results[i]);
        }

        lv_label_set_text(average_measurement_labels[i], buf);
        lv_obj_clear_flag(average_measurement_labels[i], LV_OBJ_FLAG_HIDDEN);
        clear_average_index_date_line(i);
    }
}




/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    update_average_display
  * @brief   更新平均值显示
  * @param   none
  * <AUTHOR>
  * @Data    2025-08-05
 **/
/* -------------------------------- end -------------------------------- */
void update_average_display(void)
{
    float final_value;
    if (get_measure_mode() != AVERAGE_MODE) {
        return;
    }

    // 检查是否完成了所有测量
    extern uint32_t average_times_now;
    extern uint32_t average_times_total;
    // 显示最终平均值
    final_value = get_result();

    // 根据当前单位转换数值
    float display_value = final_value;
    if (get_measure_unit() == RESULT1M_MODE) {
        // Convert to mg/dL: μmol/L * 17.1 = mg/dL
        display_value = final_value * 17.1f;
    }

    // 显示在下方
    char buf[32];
    if (get_measure_unit() == RESULT1M_MODE) {
        snprintf(buf, sizeof(buf), "%.2f", display_value);  // mg/dL显示1位小数
    } else {
        snprintf(buf, sizeof(buf), "%.2f", display_value);  // μmol/L显示2位小数
    }
    lv_label_set_text(ui.result1_label_2, buf);

    // 确保数值标签可见
    lv_obj_clear_flag(ui.result1_label_2, LV_OBJ_FLAG_HIDDEN);

    // 重新绘制渐变条和圆点
    draw_gradient_line_circle_anim(ui.result1, display_value);
}


static void draw_percent_cb(void *var, int32_t value)
{
    LV_UNUSED(var);
    uint8_t percent = (uint8_t)value;


    // 恢复背景（用 memcpy 比 fill + for 更快）
    memcpy(canvas_buf, bg_buf, sizeof(canvas_buf));
    lv_obj_invalidate(canvas);  // 通知 LVGL 重绘对象


    // 计算位置
    if (percent < 0) percent = 0;
    if (percent > 100) percent = 100;
    int min_x = INDICATOR_RADIUS;
    int max_x = GRADIENT_WIDTH - 1 - INDICATOR_RADIUS;
    int x = min_x + (max_x - min_x) * percent / 100;
    int cx = x;
    int cy = CANVAS_HEIGHT / 2;

    // 获取当前颜色
    lv_color_t color = lv_canvas_get_px(canvas, x, cy);

    // 画圆
    int radius = INDICATOR_RADIUS;
    for (int py = cy - radius; py <= cy + radius; py++) {
        for (int px = cx - radius; px <= cx + radius; px++) {
            int dx = px - cx;
            int dy = py - cy;
            if (dx * dx + dy * dy <= radius * radius) {
                if (px >= 0 && px < GRADIENT_WIDTH && py >= 0 && py < CANVAS_HEIGHT)
                    lv_canvas_set_px(canvas, px, py, color);
            }
        }
    }
}


void draw_gradient_line_circle_anim(lv_obj_t *parent, float bilirubin)
{
    canvas = lv_canvas_create(parent);
    lv_canvas_set_buffer(canvas, canvas_buf, GRADIENT_WIDTH, CANVAS_HEIGHT, LV_IMG_CF_TRUE_COLOR);
    lv_obj_align(canvas, LV_ALIGN_BOTTOM_MID, 0, -10);
    int percent = 0;
    if (bilirubin > 0.0f) {
        // 根据测量单位定义不同的最大值范围
        float max_value = 0.0f;
        if (get_measure_unit() == RESULT1_MODE) {
            // mg/dL模式：最大值为32 mg/dL
            max_value = 32.0f;
        } else {
            // μmol/L模式：假设最大值为544μmol/L
            max_value = 544.0f;
        }

        // 计算百分比，限制在0-100范围内
        percent = (uint8_t)((bilirubin / max_value) * 100.0f);
        if (percent > 100) {
            percent = 100;
        }
    }

    // 初始化透明背景
    lv_canvas_fill_bg(canvas, lv_color_black(), LV_OPA_TRANSP);

    // 画渐变线
    int line_y_start = CANVAS_HEIGHT / 2 - GRADIENT_HEIGHT / 2;
    for (int px = 0; px < GRADIENT_WIDTH; px++) {
        uint8_t r = (uint32_t)px * 255 / (GRADIENT_WIDTH - 1);
        uint8_t g = 255 - r;
        lv_color_t c = lv_color_make(r, g, 0);
        for (int py = line_y_start; py < line_y_start + GRADIENT_HEIGHT; py++) {
            lv_canvas_set_px(canvas, px, py, c);
        }
    }

    // 启动动画
    lv_anim_t a;
    lv_anim_init(&a);
    lv_anim_set_var(&a, NULL);  // 不需要具体变量
    lv_anim_set_exec_cb(&a, draw_percent_cb);
    lv_anim_set_values(&a, 0, percent);
    lv_anim_set_time(&a, 500);  // 动画时长 500ms
    lv_anim_set_playback_time(&a, 0);  // 无回放
    lv_anim_start(&a);

    memcpy(bg_buf, canvas_buf, sizeof(canvas_buf));
}

//数值显示
void show_bilirubin(float bilirubin)
{

    // 检查result1界面是否存在
    if (ui.result1 == NULL) {
        NRF_LOG_ERROR("show_bilirubin: result1 screen not created yet");
        return;
    }

    // 检查result1_label_2是否存在，如果不存在则创建
    if (ui.result1_label_2 == NULL) {
        ui.result1_label_2 = lv_label_create(ui.result1);
        lv_obj_set_pos(ui.result1_label_2, 37, 75);
        lv_obj_set_size(ui.result1_label_2, 199, 58);
        lv_label_set_long_mode(ui.result1_label_2, LV_LABEL_LONG_WRAP);

        // 设置样式
        lv_obj_set_style_border_width(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_radius(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_text_color(ui.result1_label_2, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(ui.result1_label_2, &lv_PingFang_regular_58, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_text_opa(ui.result1_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_text_letter_space(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_text_line_space(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_text_align(ui.result1_label_2, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_pad_top(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_pad_right(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_pad_bottom(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_pad_left(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_width(ui.result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    }

    // 更新显示内容
    if(bilirubin == 0.0f) {
        //无数字情况，显示下划线(后续可以添加ERROR)
        // 使用适配函数显示对应模式的下划线
        create_empty_result_lines(ui.result1);
        lv_label_set_text(ui.result1_label_2, "      ");
    } else {
        // 使用适配函数清除对应模式的下划线
        clear_result_lines();
        char buf[32];
        snprintf(buf, sizeof(buf), "%.2f", bilirubin);
        lv_label_set_text(ui.result1_label_2, buf);
    }

    lv_obj_clear_flag(ui.result1_label_2, LV_OBJ_FLAG_HIDDEN);
    // 强制刷新显示
    lv_obj_invalidate(ui.result1_label_2);
    lv_obj_update_layout(ui.result1);

    //其他部分显示完再进行下方圆球滑动
    lv_scr_load(ui.result1);
    // 将bilirubin值转换为百分比值用于渐变条圆点位置
	
	//在历史界面不生效
	if(sm_get() != SM_HISTORY){
		draw_gradient_line_circle_anim(ui.result1, bilirubin);

		lv_obj_update_layout(ui.result1);
	}
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    change_unit
  * @brief
  * @param   ui: lv_ui
**			 unit: sm_measure_unit_t
  * @retval
  * <AUTHOR>
  * @Data    2025-07-21
  * 1. ...
  * <Function>:Switch UI interface units
 **/
/* -------------------------------- end -------------------------------- */
extern uint32_t average_times_now;
void change_unit(sm_measure_unit_t unit)
{
    if(unit == RESULT1_MODE) {
        lv_label_set_text(ui.result1_label_3, "mg/dL");
    } else {
        lv_label_set_text(ui.result1_label_3, "umol/L");
    }
    // 如果是平均模式，需要更新底部下划线数量和重新显示数值
    if(get_measure_mode() == AVERAGE_MODE) {
        // 先清除现有的底部下划线
        clear_average_bottom_lines();
        if(average_times_now < 3) {
            // 根据新的测量单位重新创建底部下划线
            int line_count = (unit == RESULT1_MODE) ? 3 : 2;  // μmol/L显示3条，mg/dL显示2条
            create_average_bottom_lines(ui.result1, line_count);
        }
        // 安全地更新已存在标签的单位显示，不删除标签
        update_average_measurement_labels_unit();
        // 如果已完成所有测量，更新平均值显示
        if (average_times_now >= 3) {
            update_average_display();
        }

        // 强制刷新显示
        lv_obj_invalidate(ui.result1);
    }else{
		if(get_measure_unit() == RESULT1M_MODE){
				show_bilirubin(get_result() * 17.1f);
		}else show_bilirubin(get_result());
	}
	
	//上报APP
	
}


static lv_obj_t *battery_cells[4];  // 四格电量
static lv_obj_t *battery_cont;      // 电池主体
/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    battery_create
  * @brief
  * @param   parent: [lv_obj_t *]
  * @retval
  * <AUTHOR>
  * @Data    2025-07-21
  * 1. ...
  * <Function>:Drawing the Fuel Gauge
待完善
 **/
/* -------------------------------- end -------------------------------- */
void battery_create(lv_obj_t *parent)
{
    // === 电池外壳 ===
    battery_cont = lv_obj_create(parent);
    lv_obj_set_size(battery_cont, 29, 19);  // 电池主体尺寸
    lv_obj_set_pos(battery_cont, 274, 2);   // 电池位置保持不变
    lv_obj_set_style_radius(battery_cont, 3, 0);  // 圆角也相应增大
	
    lv_obj_set_style_bg_color(battery_cont, lv_color_black(), 0);
    lv_obj_set_style_bg_opa(battery_cont, LV_OPA_30, 0);
	lv_obj_set_style_border_width(battery_cont, 2, 0);
    lv_obj_set_style_border_color(battery_cont, lv_color_white(), 0);
    lv_obj_set_style_pad_all(battery_cont, 0, 0);  // 不使用padding，手动控制
    lv_obj_clear_flag(battery_cont, LV_OBJ_FLAG_SCROLLABLE);

    // === 电池头（正极） ===
    lv_obj_t *battery_head = lv_obj_create(parent);
	lv_obj_set_size(battery_head, 3, 10);
    lv_obj_set_style_border_width(battery_head, 0, 0);
    lv_obj_set_style_radius(battery_head, 1, 0);
    lv_obj_set_style_bg_color(battery_head, lv_color_white(), 0);
    lv_obj_set_style_bg_opa(battery_head, LV_OPA_COVER, 0);
    lv_obj_clear_flag(battery_head, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_align_to(battery_head, battery_cont, LV_ALIGN_OUT_LEFT_MID, -1, 0);

    // === 电池格子 ===
    // 重新设计：考虑边框宽度，精确计算位置

    // 电池容器参数
    const int border_width = 2;     // 电池容器边框宽度
    const int inner_margin = 2;     // 电池格与内壁的间距

    // 电池格参数
    const int cell_gap = 1;         // 格子间隙
	const int cell_width = 5;       // 每格宽度
    const int cell_height = 12;     // 每格高度

    // 计算起始位置
	const int start_x = border_width + inner_margin;  // X起始位置 = 2 + 2 = 4
    const int start_y = border_width - 1;             // Y起始位置 = 1

    // 从右到左的位置计算
    for (int i = 0; i < 4; i++) {
        battery_cells[i] = lv_obj_create(battery_cont);

        // 微调坐标计算：再往左移一点点
        // 格子顺序：[3] [2] [1] [0] (从左到右的视觉顺序)
        int adjusted_start_x = 1;
        int x_pos = adjusted_start_x + (3 - i) * (cell_width + cell_gap);
        int y_pos = start_y;

        lv_obj_set_pos(battery_cells[i], x_pos, y_pos);
        lv_obj_set_size(battery_cells[i], cell_width, cell_height);

        // 样式设置
        lv_obj_set_style_border_width(battery_cells[i], 0, 0);
        lv_obj_set_style_bg_color(battery_cells[i], lv_color_white(), 0);
        lv_obj_set_style_bg_opa(battery_cells[i], LV_OPA_COVER, 0);
        lv_obj_clear_flag(battery_cells[i], LV_OBJ_FLAG_SCROLLABLE);

        if (i == 0) {
            // 最右边格：右圆角
			lv_obj_set_style_radius(battery_cells[i], 2, 0);
        } else if (i == 3) {
            // 最左边格：左圆角
            lv_obj_set_style_radius(battery_cells[i], 2, 0);
        } else {
            // 中间格：无圆角
            lv_obj_set_style_radius(battery_cells[i], 0, 0);
        }

        // 初始状态：隐藏
        lv_obj_add_flag(battery_cells[i], LV_OBJ_FLAG_HIDDEN);
    }
}
/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    battery_set_level
  * @brief
  * @param   percent: [uint8_t]
  * @retval
  * <AUTHOR>
  * @Data    2025-07-21
  * 1. ...
  * <Function>:Pass in a battery value between 0 and 100 to update the battery bar
 **/
/* -------------------------------- end -------------------------------- */

/**
 * @brief 设置电池电量显示 - 重构版本
 * @param percent 电量百分比 (0-100)
 *
 * 显示逻辑：
 * - 0-24%: 不显示任何格子
 * - 25-49%: 显示最右边1格 (cell[0])
 * - 50-74%: 显示最右边2格 (cell[0], cell[1])
 * - 75-99%: 显示最右边3格 (cell[0], cell[1], cell[2])
 * - 100%: 显示全部4格 (cell[0], cell[1], cell[2], cell[3])
 */
void battery_set_level(uint8_t percent)
{
    // 参数检查
    if (percent > 100) {
        percent = 100;
    }

    // 计算需要显示的格子数量
    int level = (percent + 24) / 25;  // 四舍五入到最近的25%
    if (level > 4) level = 4;

    // 检查电池格是否已创建
    for (int i = 0; i < 4; i++) {
        if (battery_cells[i] == NULL) {
            return;
        }
    }

    // 设置电池格显示状态
    for (int i = 0; i < 4; i++) {
        if (i < level) {
            // 显示这个格子（从右边开始：0,1,2,3）
            lv_obj_clear_flag(battery_cells[i], LV_OBJ_FLAG_HIDDEN);
        } else {
            // 隐藏这个格子
            lv_obj_add_flag(battery_cells[i], LV_OBJ_FLAG_HIDDEN);
        }

        // 立即刷新每个格子
        lv_obj_invalidate(battery_cells[i]);
    }

    // 刷新整个电池容器
    if (battery_cont != NULL) {
        lv_obj_invalidate(battery_cont);
    }
}

// 全局变量：存储数字标签对象
static lv_obj_t *number_square = NULL;
static lv_obj_t *number_label = NULL;


/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    number_square_create
  * @brief
  * @param   parent: [输入/出]
**			 number: [输入/出]
  * @retval
  * <AUTHOR>
  * @Data    2025-07-24
  * 1. ...
  * <Function>:creat square
 **/
/* -------------------------------- end -------------------------------- */

void number_square_create(lv_obj_t *parent, int number)
{
// === 圆角正方形容器 ===
    number_square = lv_obj_create(parent);

    // 尺寸：正方形，高度与电池框相同
	const int square_size = 19;
    lv_obj_set_size(number_square, square_size, square_size);

    // 位置：放在电池左侧，留一点间隙
    const int battery_x = 270;  // 电池X坐标
    const int battery_y = 2;    // 电池Y坐标
    const int gap = 4;          // 与电池的间隙
    lv_obj_set_pos(number_square, battery_x - square_size - gap, battery_y);

    // 样式设置：只有边框，中间透明
	lv_obj_set_style_radius(number_square, 5, 0);
    lv_obj_set_style_bg_opa(number_square, LV_OPA_TRANSP, 0);  // 背景透明
    lv_obj_set_style_border_width(number_square, 2, 0);        // 边框宽度
    lv_obj_set_style_border_color(number_square, lv_color_white(), 0);  // 白色边框
    lv_obj_set_style_pad_all(number_square, 0, 0);
    lv_obj_clear_flag(number_square, LV_OBJ_FLAG_SCROLLABLE);

    // === 数字标签 ===
    number_label = lv_label_create(number_square);

    // 设置数字文本
    static char number_text[4];  // 静态缓冲区
    lv_snprintf(number_text, sizeof(number_text), "%d", number);
    lv_label_set_text(number_label, number_text);

    // 标签样式：白色数字
    lv_obj_set_style_text_color(number_label, lv_color_white(), 0);  // 白色字体
    lv_obj_set_style_text_font(number_label, &lv_font_montserrat_12, 0);  // 使用12号字体
    lv_obj_set_style_text_align(number_label, LV_TEXT_ALIGN_CENTER, 0);

    // 居中对齐
    lv_obj_center(number_label);
}

/**
 * @brief 更新数字标签显示的数字
 * @param number 新的数字
 */
void number_square_set_number(int number)
{
    if (number_label == NULL) {
        return;
    }

    static char number_text[4];
    lv_snprintf(number_text, sizeof(number_text), "%d", number);
    lv_label_set_text(number_label, number_text);
}

// 全局变量：存储蓝牙图片对象
static lv_obj_t *bluetooth_image = NULL;

/**
 * @brief 创建蓝牙图片显示
 * @param parent 父对象
 *
 * 设计说明：
 * - 使用图片资源 _bluetooth_alpha_18x18
 * - 尺寸调整为与之前方案一致（16x16）
 */
void bluetooth_image_create(lv_obj_t *parent)
{
    // === 创建图片对象 ===
    bluetooth_image = lv_img_create(parent);

    // 设置图片源
    lv_img_set_src(bluetooth_image, &_bluetooth_alpha_22x22);

    // 设置位置：固定坐标
    lv_obj_set_pos(bluetooth_image, 220, 0);

    // 图片样式设置
    lv_obj_set_style_img_opa(bluetooth_image, LV_OPA_COVER, 0);  // 完全不透明
    lv_obj_set_style_radius(bluetooth_image, 0, 0);             // 无圆角
    lv_obj_set_style_border_width(bluetooth_image, 0, 0);       // 无边框
    lv_obj_set_style_pad_all(bluetooth_image, 0, 0);            // 无内边距

    // 清除不需要的标志
    lv_obj_clear_flag(bluetooth_image, LV_OBJ_FLAG_SCROLLABLE);
}

/**
 * @brief 显示或隐藏蓝牙图片
 * @param visible true显示，false隐藏
 */
void bluetooth_image_set_visible(bool visible)
{
    if (bluetooth_image == NULL) {
        NRF_LOG_WARNING("BluetoothImage: Image not created yet");
        return;
    }

    if (visible) {
        lv_obj_clear_flag(bluetooth_image, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(bluetooth_image, LV_OBJ_FLAG_HIDDEN);
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    reset_average_mode_pointers
  * @brief   重置平均模式界面元素指针
  * @param   None
  * @retval  None
  * <AUTHOR>
  * @Data    2025-07-31
  * <Function>: Reset pointers for average mode UI elements
 **/
/* -------------------------------- end -------------------------------- */
void reset_average_mode_pointers(void)
{
    // 重置日期位置细下划线指针
    for (int i = 0; i < AVERAGE_DATE_LINE_COUNT; i++) {
        average_date_line_objs[i] = NULL;
    }

    // 重置底部粗下划线指针
    for (int i = 0; i < LINE_COUNT; i++) {
        average_bottom_line_objs[i] = NULL;
    }
}
/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    update_single_mode_date_display
  * @brief   更新单次测量模式下的日期显示
  * <AUTHOR>
  * @Data    2025-08-13
 **/
/* -------------------------------- end -------------------------------- */
void update_single_mode_date_display(void)
{
    // 只在单次模式下更新日期显示
    if (get_measure_mode() != SINGLE_MODE) {
        return;
    }

    // 检查result1界面是否存在
    if (ui.result1 == NULL || ui.result1_label_1 == NULL) {
        return;
    }

    // 获取当前时间
    Tm current_time = get_local_time();

    // 格式化日期字符串 (YYYY / MM / DD)
    char date_str[32];
    snprintf(date_str, sizeof(date_str), "%04d / %02d / %02d",
             current_time.tm_year,
             current_time.tm_mon,
             current_time.tm_mday);

    // 更新日期标签
    lv_label_set_text(ui.result1_label_1, date_str);
}


//上升/下降指示
/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    line_anim_step
  * @brief   上升/下降指示动画驱动器回调
  * <AUTHOR>
  * @Data    2025-08-13
 **/
/* -------------------------------- end -------------------------------- */
static void line_anim_step(lv_timer_t * t)
{
    line_anim_ctx_t * ctx = (line_anim_ctx_t *) t->user_data;

    if(ctx->point_index < LINE_TOTAL_POINTS) {
        ctx->point_index++;
        lv_line_set_points(ctx->line_obj, ctx->points, ctx->point_index);
    } else {
        lv_timer_del(ctx->anim_timer);
        ctx->anim_timer = NULL;
        // 动画完成，解锁测量
        s_line_animating = false;
    }
}

/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    prepare_line
  * @brief
  * @param   ctx: 第几条线		0，1，2
**			 parent: 当前活跃屏幕
**			 type: 线条类型		LINE_TYPE_HORIZON，LINE_TYPE_DOWN，LINE_TYPE_UP
**			 start_x: x坐标
**			 start_y: y坐标
  * @retval
  * <AUTHOR>
  * @Data    2025-08-13
 **/
/* -------------------------------- end -------------------------------- */

static void prepare_line(line_anim_ctx_t * ctx, lv_obj_t * parent, line_type_t type, lv_coord_t start_x, lv_coord_t start_y)
{
    ctx->point_index = 2;  // 从2个点开始，形成一条短线
    ctx->type = type;

    // 先给 current_line 赋值
    if(type == LINE_TYPE_HORIZON) current_line = 0;
    else if(type == LINE_TYPE_DOWN) current_line = 1;
    else current_line = 2;

    // 精确的尺寸控制：整个图像32×19像素
    const int total_width = 32;   // 整个图像宽度（最左到最右）
    const int total_height = 19;  // 整个图像高度（最上到最下）

    // 计算X轴步长
    float step_x = (float)total_width / (LINE_TOTAL_POINTS - 1);

    // 分段点计算
    int segment1_end = LINE_TOTAL_POINTS / 3;      // 1/3处
    int segment2_end = (LINE_TOTAL_POINTS * 2) / 3; // 2/3处

    for(int i = 0; i < LINE_TOTAL_POINTS; i++) {
        ctx->points[i].x = (int)(i * step_x);

        switch(type) {
            case LINE_TYPE_HORIZON:
                // 水平线
                ctx->points[i].y = 0;
                break;

            case LINE_TYPE_DOWN:
                // 下箭头：向下-向上-向下（带箭头）
                if(i <= segment1_end) {
                    // 第一段：45度向下（从中间向下到底部）
                    float progress = (float)i / segment1_end;
                    ctx->points[i].y = total_height/2 + (int)(progress * total_height/2);
                } else if(i <= segment2_end) {
                    // 第二段：45度向上（从底部回到中间）
                    float progress = (float)(i - segment1_end) / (segment2_end - segment1_end);
                    ctx->points[i].y = total_height - 1 - (int)(progress * total_height/2);
                } else {
                    // 第三段：继续45度向下并在末端添加箭头（±30°翼），与尾端无缝衔接
                    const int arrow_reserved_points = 4; // [尖端, 左翼端, 尖端重复, 右翼端]
                    const int tip_index = LINE_TOTAL_POINTS - arrow_reserved_points;
                    const int tip_x = total_width - 1;
                    const int tip_y = total_height - 1;

                    if(i < tip_index) {
                        // 直线插值到尖端，保证线段为直线并与尖端无缝衔接
                        float progress = (float)(i - segment2_end) / (tip_index - segment2_end);
                        int start_x_third = (int)(segment2_end * step_x);
                        ctx->points[i].x = (int)( (float)start_x_third + progress * (tip_x - (float)start_x_third) );
                        ctx->points[i].y = (int)( (float)total_height/2 + progress * (tip_y - (float)total_height/2) );
                    } else if(i == tip_index) {
                        // 尖端（同时作为主线末端）
                        ctx->points[i].x = tip_x;
                        ctx->points[i].y = tip_y;
                    } else {
                        // 箭头两翼端点：与最后线段夹角±30°（相对45°主方向 → 15°与75°）
                        int local = i - tip_index; // 1:左翼端  2:尖端重复  3:右翼端
                        const int arrow_length = 8;
                        switch(local) {
                            case 1: { // 左翼端（15°）
                                // cos(15°)≈0.966, sin(15°)≈0.259
                                int dx = (int)(arrow_length * 0.966f);
                                int dy = (int)(arrow_length * 0.259f);
                                ctx->points[i].x = tip_x - dx;
                                ctx->points[i].y = tip_y - dy;
                                break;
                            }
                            case 2: { // 尖端重复，避免左右翼之间连线
                                ctx->points[i].x = tip_x;
                                ctx->points[i].y = tip_y;
                                break;
                            }
                            case 3: { // 右翼端（75°）
                                // cos(75°)≈0.259, sin(75°)≈0.966
                                int dx = (int)(arrow_length * 0.259f);
                                int dy = (int)(arrow_length * 0.966f);
                                ctx->points[i].x = tip_x - dx;
                                ctx->points[i].y = tip_y - dy;
                                break;
                            }
                        }
                    }
                }
                break;

            case LINE_TYPE_UP:
                // 上箭头：向上-向下-向上（带箭头）
                // 注意：LVGL坐标系Y=0在顶部，正值向下，所以"向上"是负值
                if(i <= segment1_end) {
                    // 第一段：45度向上（从中间向上到顶部）
                    float progress = (float)i / segment1_end;
                    ctx->points[i].y = total_height/2 - (int)(progress * total_height/2);
                } else if(i <= segment2_end) {
                    // 第二段：45度向下（从顶部回到中间）
                    float progress = (float)(i - segment1_end) / (segment2_end - segment1_end);
                    ctx->points[i].y = 0 + (int)(progress * total_height/2);
                } else {
                    // 第三段：继续45度向上并在末端添加箭头（±30°翼），与尾端无缝衔接
                    const int arrow_reserved_points = 4; // [尖端, 左翼端, 尖端重复, 右翼端]
                    const int tip_index = LINE_TOTAL_POINTS - arrow_reserved_points;
                    const int tip_x = total_width - 1;
                    const int tip_y = 0;

                    if(i < tip_index) {
                        // 直线插值到尖端，保证线段为直线并与尖端无缝衔接
                        float progress = (float)(i - segment2_end) / (tip_index - segment2_end);
                        int start_x_third = (int)(segment2_end * step_x);
                        ctx->points[i].x = (int)( (float)start_x_third + progress * (tip_x - (float)start_x_third) );
                        ctx->points[i].y = (int)( (float)total_height/2 + progress * (tip_y - (float)total_height/2) );
                    } else if(i == tip_index) {
                        // 尖端（同时作为主线末端）
                        ctx->points[i].x = tip_x;
                        ctx->points[i].y = tip_y;
                    } else {
                        // 箭头两翼端点：相对-45°主方向±30° → -15°与-75°
                        int local = i - tip_index; // 1:左翼端  2:尖端重复  3:右翼端
                        const int arrow_length = 8;
                        switch(local) {
                            case 1: { // 左翼端（-75°）
                                // cos(-75°)≈0.259, sin(-75°)≈-0.966
                                int dx = (int)(arrow_length * 0.259f);
                                int dy = (int)(arrow_length * 0.966f); // 向下为正
                                ctx->points[i].x = tip_x - dx;
                                ctx->points[i].y = tip_y + dy;
                                break;
                            }
                            case 2: { // 尖端重复，避免左右翼之间连线
                                ctx->points[i].x = tip_x;
                                ctx->points[i].y = tip_y;
                                break;
                            }
                            case 3: { // 右翼端（-15°）
                                // cos(-15°)≈0.966, sin(-15°)≈-0.259
                                int dx = (int)(arrow_length * 0.966f);
                                int dy = (int)(arrow_length * 0.259f); // 向下为正
                                ctx->points[i].x = tip_x - dx;
                                ctx->points[i].y = tip_y + dy;
                                break;
                            }
                        }
                    }
                }
                break;
        }
    }

    ctx->line_obj = lv_line_create(parent);
    lv_line_set_points(ctx->line_obj, ctx->points, ctx->point_index);
    lv_obj_align(ctx->line_obj, LV_ALIGN_TOP_LEFT, start_x, start_y);

    static lv_style_t style_line;
    static bool style_inited = false;
    if(!style_inited) {
        lv_style_init(&style_line);
        lv_style_set_line_width(&style_line, 2);
        lv_style_set_line_color(&style_line, lv_palette_main(LV_PALETTE_BLUE));
        lv_style_set_line_rounded(&style_line, false); // 箭头需要尖锐的角
        style_inited = true;
    }
    lv_obj_add_style(ctx->line_obj, &style_line, 0);

    // 创建定时器并上锁
    ctx->anim_timer = lv_timer_create(line_anim_step, LINE_INTERVAL_MS, ctx);
    s_line_animating = true;
}

//外部可调用的上升/下降指示条
void show_prepare_line(line_type_t type) {
    switch(type) {
    case LINE_TYPE_HORIZON:
        prepare_line(&line_ctx[0], lv_scr_act(), LINE_TYPE_HORIZON, 234, 95);
        break;
    case LINE_TYPE_DOWN:
        prepare_line(&line_ctx[1], lv_scr_act(), LINE_TYPE_DOWN, 234, 80);
        break;
    case LINE_TYPE_UP:
        prepare_line(&line_ctx[2], lv_scr_act(), LINE_TYPE_UP, 234, 90);
        break;
    }
}

bool gui_is_line_animating(void)
{
    return s_line_animating;
}