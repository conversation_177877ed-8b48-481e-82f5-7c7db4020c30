/**
 * @file battery_adc_new.c
 * @brief 电池ADC测量模块 - 兼容CW2015 + ADC读取P0.3
 * 
 * 本模块实现两个核心功能：
 * 1. 兼容TWI CW2015电量计芯片的接口
 * 2. 使用ADC读取P0.3引脚电压并转换为电量百分比
 * 
 * 主要功能：
 * - 完全兼容CW2015接口，无需修改现有代码
 * - 12位ADC精度测量P0.3引脚电压
 * - 8点移动平均滤波算法
 * - 基于锂电池放电曲线的电量转换
 * - 自动校准和定时监控
 * - 低电量保护和状态管理
 */

#include "battery_adc.h"
#include "nrf_drv_saadc.h"
#include "nrf_gpio.h"
#include "app_timer.h"
#include "nrf_log.h"
#include "sm.h"
#include "gui.h"
#include <string.h>

// ==================== 私有变量 ====================

// 模块状态
static bool m_battery_initialized = false;
static bool m_timer_active = false;

// 电池数据
static uint8_t m_soc_percent = 0;                    // 当前电量百分比
static uint16_t m_voltage_mv = 0;                    // 当前电压 (mV)
static battery_adc_info_t m_battery_info = {0};     // 完整电池信息

// 校准参数
static int16_t m_calibration_offset_mv = BATTERY_CALIBRATION_OFFSET;    // 校准偏移量 (mV)
static uint16_t m_calibration_gain_permille = BATTERY_CALIBRATION_GAIN; // 校准增益 (千分比)

// 移动平均滤波器
static uint16_t m_filter_buffer[BATTERY_FILTER_SIZE];
static uint8_t m_filter_index = 0;
static bool m_filter_full = false;
static uint32_t m_filter_sum = 0;

// 定时器
APP_TIMER_DEF(m_battery_timer_id);

// 电压-电量查找表 (基于锂电池放电曲线)
static const struct {
    uint16_t voltage_mv;
    uint8_t percent;
} voltage_percent_table[] = {
    {4200, 100}, {4150, 95},  {4100, 90},  {4050, 85},  {4000, 80},
    {3950, 75},  {3900, 70},  {3850, 65},  {3800, 60},  {3750, 55},
    {3700, 50},  {3650, 45},  {3600, 40},  {3550, 35},  {3500, 30},
    {3450, 25},  {3400, 20},  {3350, 15},  {3300, 10},  {3250, 5},
    {3000, 0}
};

#define VOLTAGE_TABLE_SIZE (sizeof(voltage_percent_table) / sizeof(voltage_percent_table[0]))

// ==================== 私有函数声明 ====================

static void battery_saadc_callback(nrf_drv_saadc_evt_t const * p_event);
static uint16_t battery_measure_voltage_raw(uint8_t sample_count);
static uint16_t battery_filter_add(uint16_t voltage_mv);
static void battery_update_info(void);
static void battery_timer_handler(void *p_context);

// ==================== SAADC回调函数 ====================

/**
 * @brief SAADC事件回调函数
 */
static void battery_saadc_callback(nrf_drv_saadc_evt_t const * p_event)
{
    UNUSED_PARAMETER(p_event);
}

// ==================== 滤波器函数 ====================

/**
 * @brief 初始化移动平均滤波器
 */
static void battery_filter_init(void)
{
    m_filter_index = 0;
    m_filter_full = false;
    m_filter_sum = 0;
    
    for (int i = 0; i < BATTERY_FILTER_SIZE; i++) {
        m_filter_buffer[i] = 0;
    }
}

/**
 * @brief 添加新的电压值到滤波器
 * 
 * @param voltage_mv 电压值 (mV)
 * @return uint16_t 滤波后的电压值 (mV)
 */
static uint16_t battery_filter_add(uint16_t voltage_mv)
{
    // 移除旧值
    if (m_filter_full) {
        m_filter_sum -= m_filter_buffer[m_filter_index];
    }
    
    // 添加新值
    m_filter_buffer[m_filter_index] = voltage_mv;
    m_filter_sum += voltage_mv;
    
    // 更新索引
    m_filter_index = (m_filter_index + 1) % BATTERY_FILTER_SIZE;
    if (m_filter_index == 0) {
        m_filter_full = true;
    }
    
    // 计算平均值
    uint8_t count = m_filter_full ? BATTERY_FILTER_SIZE : m_filter_index;
    return (count > 0) ? (m_filter_sum / count) : voltage_mv;
}

/**
 * @brief 重置滤波器
 */
void battery_filter_reset(void)
{
    battery_filter_init();
}

// ==================== 电压测量函数 ====================

/**
 * @brief 测量原始电池电压
 * 
 * @param sample_count 采样次数
 * @return uint16_t 测量的电压值 (mV)
 */
static uint16_t battery_measure_voltage_raw(uint8_t sample_count)
{
    if (!m_battery_initialized) {
        return 0;
    }
    
    nrf_saadc_value_t adc_value = 0;
    uint32_t sum = 0;
    uint8_t valid_samples = 0;
    // 多次采样取平均
    for (uint8_t i = 0; i < sample_count; i++) {
		uint32_t timeout_count = 0;
		while (nrf_drv_saadc_is_busy() && timeout_count < 1000) {
            timeout_count++;
            nrf_delay_us(1);
        }
        ret_code_t ret = nrf_drv_saadc_sample_convert(BATTERY_ADC_CHANNEL, &adc_value);
        if (ret == NRF_SUCCESS && adc_value > 0) {
            sum += adc_value;
            valid_samples++;
        }
        NRF_LOG_INFO("adc%d: %d",i,(uint32_t)adc_value);
        // 采样间隔
        nrf_delay_us(50);
    }
    
    if (valid_samples == 0) {
        return 0;
    }
    // 计算平均ADC值
    uint16_t avg_adc = sum / valid_samples;
//    NRF_LOG_INFO("adc: %d",avg_adc);
    // ADC值转换为电压 (mV)
    // nRF52840 ADC: 12位分辨率，内部参考电压0.6V，增益1/6
    // 电压 = ADC值 * (0.6V / 4096) * 6 * 1000 = ADC值 * 0.879
//    uint32_t voltage_mv = (avg_adc * 879) / 1000;
    uint32_t voltage_mv = avg_adc;
    // 应用校准参数
    voltage_mv = (voltage_mv * m_calibration_gain_permille) / 1000 + m_calibration_offset_mv;
    
    return (uint16_t)voltage_mv;
}

// ==================== 电量转换函数 ====================

/**
 * @brief 电压转换为电量百分比
 * 
 * @param voltage_mv 电池电压 (mV)
 * @return uint8_t 电量百分比 (0-100)
 */
uint8_t battery_voltage_to_percent(uint16_t voltage_mv)
{
    // 边界检查
    if (voltage_mv >= BATTERY_VOLTAGE_MAX_MV) {
        return 100;
    }
    if (voltage_mv <= BATTERY_VOLTAGE_MIN_MV) {
        return 0;
    }
    
    // 查找表插值
    for (int i = 0; i < VOLTAGE_TABLE_SIZE - 1; i++) {
        if (voltage_mv >= voltage_percent_table[i + 1].voltage_mv) {
            // 线性插值
            uint16_t v1 = voltage_percent_table[i + 1].voltage_mv;
            uint16_t v2 = voltage_percent_table[i].voltage_mv;
            uint8_t p1 = voltage_percent_table[i + 1].percent;
            uint8_t p2 = voltage_percent_table[i].percent;
            
            uint8_t percent = p1 + ((voltage_mv - v1) * (p2 - p1)) / (v2 - v1);
            return percent;
        }
    }
    
    return 0;
}

/**
 * @brief 根据电量百分比获取电池状态
 * 
 * @param percent 电量百分比
 * @return battery_status_t 电池状态
 */
battery_status_t battery_get_status_from_percent(uint8_t percent)
{
    if (percent == 0) {
        return BATTERY_STATUS_EMPTY;
    } else if (percent <= BATTERY_LEVEL_CRITICAL) {
        return BATTERY_STATUS_CRITICAL;
    } else if (percent <= BATTERY_LEVEL_LOW) {
        return BATTERY_STATUS_LOW;
    } else if (percent <= BATTERY_LEVEL_MEDIUM) {
        return BATTERY_STATUS_MEDIUM;
    } else if (percent < BATTERY_LEVEL_FULL) {
        return BATTERY_STATUS_HIGH;
    } else {
        return BATTERY_STATUS_FULL;
    }
}

/**
 * @brief 电池状态转换为字符串
 * 
 * @param status 电池状态
 * @return const char* 状态字符串
 */
const char* battery_status_to_string(battery_status_t status)
{
    switch (status) {
        case BATTERY_STATUS_EMPTY:    return "Empty";
        case BATTERY_STATUS_CRITICAL: return "Critical";
        case BATTERY_STATUS_LOW:      return "Low";
        case BATTERY_STATUS_MEDIUM:   return "Medium";
        case BATTERY_STATUS_HIGH:     return "High";
        case BATTERY_STATUS_FULL:     return "Full";
        default:                      return "Unknown";
    }
}

// ==================== 更新电池信息 ====================

/**
 * @brief 更新电池信息
 */
static void battery_update_info(void)
{
    // 测量原始电压
    uint16_t raw_voltage = battery_measure_voltage_raw(BATTERY_SAMPLE_COUNT);
    
    // 滤波处理
    uint16_t filtered_voltage = battery_filter_add(raw_voltage);
    
    // 转换为电量百分比
    uint8_t percent = battery_voltage_to_percent(filtered_voltage);
    
    // 更新全局变量
    m_voltage_mv = filtered_voltage;
    m_soc_percent = percent;
    
    // 更新电池信息结构体
    m_battery_info.voltage_raw_mv = raw_voltage;
    m_battery_info.voltage_filtered_mv = filtered_voltage;
    m_battery_info.level_percent = percent;
    m_battery_info.status = battery_get_status_from_percent(percent);
    m_battery_info.is_valid = (raw_voltage > 0);
    m_battery_info.timestamp = app_timer_cnt_get();
    
    NRF_LOG_INFO("Battery: %dmV (%d%%) - %s", 
                  filtered_voltage, percent, 
                  battery_status_to_string(m_battery_info.status));
}

/**
 * @brief 电池定时器回调函数
 * 
 * @param p_context 上下文指针
 */
static void battery_timer_handler(void *p_context)
{
    UNUSED_PARAMETER(p_context);
    
    if (!m_battery_initialized) {
        return;
    }
    
    // 更新电池信息
    battery_update_info();
    
    // 检查低电量状态
    if (m_battery_info.status == BATTERY_STATUS_CRITICAL) {
        // 跳转到低电量状态
        sm_jump(SM_LOWSOC, 0);
    }
}

// ==================== 初始化和反初始化函数 ====================

/**
 * @brief 初始化电池ADC模块
 *
 * @return ret_code_t 返回码
 */
ret_code_t battery_adc_init(void)
{
    ret_code_t ret_code;

    if (m_battery_initialized) {
        return NRF_SUCCESS;
    }

    // 配置ADC通道 - P0.3引脚
    nrf_saadc_channel_config_t channel_config = NRF_DRV_SAADC_DEFAULT_CHANNEL_CONFIG_SE(BATTERY_ADC_PIN);
    channel_config.gain = BATTERY_ADC_GAIN;
    channel_config.reference = BATTERY_ADC_REFERENCE;
    channel_config.acq_time = BATTERY_ADC_ACQTIME;
    channel_config.mode = NRF_SAADC_MODE_SINGLE_ENDED;
    channel_config.burst = NRF_SAADC_BURST_DISABLED;

    ret_code = nrf_drv_saadc_channel_init(BATTERY_ADC_CHANNEL, &channel_config);
    if (ret_code != NRF_SUCCESS) {
        nrf_drv_saadc_uninit();
        return ret_code;
    }

    // 初始化滤波器
    battery_filter_init();

    // 创建定时器
    ret_code = app_timer_create(&m_battery_timer_id, APP_TIMER_MODE_REPEATED, battery_timer_handler);
    if (ret_code != NRF_SUCCESS) {
        nrf_drv_saadc_uninit();
        return ret_code;
    }

    // 启动定时器
    ret_code = app_timer_start(m_battery_timer_id, APP_TIMER_TICKS(BATTERY_UPDATE_INTERVAL_MS), NULL);
    if (ret_code != NRF_SUCCESS) {
        nrf_drv_saadc_uninit();
        return ret_code;
    }

    m_battery_initialized = true;
    m_timer_active = true;

//    // 立即更新一次电池信息
//    battery_update_info();

    NRF_LOG_INFO("Battery ADC module initialized successfully");
    return NRF_SUCCESS;
}

/**
 * @brief 反初始化电池ADC模块
 */
void battery_adc_deinit(void)
{
    if (!m_battery_initialized) {
        return;
    }

    // 停止定时器
    if (m_timer_active) {
        app_timer_stop(m_battery_timer_id);
        m_timer_active = false;
    }

    // 反初始化SAADC
    nrf_drv_saadc_uninit();

    // 重置状态
    m_battery_initialized = false;

    // 重置滤波器
    battery_filter_reset();

    NRF_LOG_INFO("Battery ADC module deinitialized");
}

// ==================== 公共接口函数 ====================

/**
 * @brief 获取完整的电池信息
 *
 * @param info 电池信息结构体指针
 * @return ret_code_t 返回码
 */
ret_code_t battery_adc_get_info(battery_adc_info_t *info)
{
    if (info == NULL) {
        return NRF_ERROR_NULL;
    }

    if (!m_battery_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    // 更新电池信息
    battery_update_info();

    // 复制信息
    *info = m_battery_info;

    return NRF_SUCCESS;
}

/**
 * @brief 测量原始电池电压 (公共接口)
 *
 * @param sample_count 采样次数
 * @return uint16_t 电池电压 (mV)，0表示测量失败
 */
uint16_t battery_adc_measure_raw(uint8_t sample_count)
{
    if (!m_battery_initialized) {
        return 0;
    }

    if (sample_count == 0) {
        sample_count = BATTERY_SAMPLE_COUNT;
    }

    return battery_measure_voltage_raw(sample_count);
}

/**
 * @brief 测量滤波后的电池电压
 *
 * @return uint16_t 滤波后电池电压 (mV)
 */
uint16_t battery_adc_measure_filtered(void)
{
    if (!m_battery_initialized) {
        return 0;
    }

    // 测量原始电压并添加到滤波器
    uint16_t raw_voltage = battery_measure_voltage_raw(BATTERY_SAMPLE_COUNT);
    return battery_filter_add(raw_voltage);
}

// ==================== 状态检查接口 ====================

/**
 * @brief 检查电池是否为低电量
 *
 * @return bool true表示低电量
 */
bool battery_is_low(void)
{
    if (!m_battery_initialized) {
        return false;
    }

    return (m_battery_info.status == BATTERY_STATUS_LOW ||
            m_battery_info.status == BATTERY_STATUS_CRITICAL ||
            m_battery_info.status == BATTERY_STATUS_EMPTY);
}

/**
 * @brief 检查电池是否为极低电量
 *
 * @return bool true表示极低电量
 */
bool battery_is_critical(void)
{
    if (!m_battery_initialized) {
        return false;
    }

    return (m_battery_info.status == BATTERY_STATUS_CRITICAL ||
            m_battery_info.status == BATTERY_STATUS_EMPTY);
}

// ==================== CW2015兼容接口 ====================

/**
 * @brief CW2015兼容初始化接口
 *
 * @return ret_code_t 返回码
 */
ret_code_t cw2015_init(void)
{
    return battery_adc_init();
}

/**
 * @brief CW2015兼容读取电量百分比接口
 *
 * @return uint8_t 电量百分比 (0-100)
 */
uint8_t cw2015_read_soc_percent(void)
{
    if (!m_battery_initialized) {
        return 0;
    }

    // 更新电池信息
    battery_update_info();

    return m_soc_percent;
}

/**
 * @brief CW2015兼容读取电压接口
 *
 * @return uint16_t 电池电压 (mV)
 */
uint16_t cw2015_read_voltage_mv(void)
{
    if (!m_battery_initialized) {
        return 0;
    }

    return m_voltage_mv;
}

/**
 * @brief CW2015兼容获取SOC接口
 *
 * @return uint8_t 电量百分比 (0-100)
 */
uint8_t get_SOC(void)
{
    return cw2015_read_soc_percent();
}

/**
 * @brief CW2015兼容读取剩余运行时间接口
 *
 * @return uint16_t 估算剩余时间 (分钟)
 */
uint16_t cw2015_read_rrt_min(void)
{
    if (!m_battery_initialized) {
        return 0;
    }

    // 假设平均功耗20mA，电池容量1000mAh
    uint16_t remaining_capacity = (m_soc_percent * 1000) / 100;
    return (remaining_capacity * 60) / 20;
}

// ==================== 校准接口 ====================

/**
 * @brief 设置校准参数
 *
 * @param offset_mv 偏移量 (mV)
 * @param gain_permille 增益 (千分比)
 */
void battery_set_calibration(int16_t offset_mv, uint16_t gain_permille)
{
    m_calibration_offset_mv = offset_mv;
    m_calibration_gain_permille = gain_permille;

    NRF_LOG_INFO("Battery calibration set: offset=%dmV, gain=%d‰",
                 offset_mv, gain_permille);
}

/**
 * @brief 获取校准参数
 *
 * @param offset_mv 偏移量指针
 * @param gain_permille 增益指针
 */
void battery_get_calibration(int16_t *offset_mv, uint16_t *gain_permille)
{
    if (offset_mv != NULL) {
        *offset_mv = m_calibration_offset_mv;
    }

    if (gain_permille != NULL) {
        *gain_permille = m_calibration_gain_permille;
    }
}

/**
 * @brief 执行ADC校准
 *
 * @return ret_code_t 返回码
 */
ret_code_t battery_adc_calibrate(void)
{
    if (!m_battery_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    // 执行SAADC校准
    ret_code_t ret = nrf_drv_saadc_calibrate_offset();
    if (ret != NRF_SUCCESS) {
        return ret;
    }

    NRF_LOG_INFO("Battery ADC calibration completed");
    return NRF_SUCCESS;
}

/**
 * @brief 重置校准参数为默认值
 */
void battery_reset_calibration(void)
{
    m_calibration_offset_mv = BATTERY_CALIBRATION_OFFSET;
    m_calibration_gain_permille = BATTERY_CALIBRATION_GAIN;

    NRF_LOG_INFO("Battery calibration reset to defaults");
}
