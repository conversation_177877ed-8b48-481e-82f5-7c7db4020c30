#include "hide_trig.h"
#include "sm.h"
#include "ble_gap.h"
//#include "pages.h"


/*
按键触发：


___-------------___---___---___---_________---___---___---__________
         1       2  3  4              5     6

1、长按触发逻辑判断
2、必须在KEY_SHORT_INTERVAL_TIME ms内紧跟KEY_SHORT_PRESS_COUNT个短按键
3、每个短按的按压时间必须小于KEY_SHORT_PRESS_TIME
4、短按的时间间隔要小于KEY_SHORT_INTERVAL_TIME ms
5、几次短按与指令号之间需要间隔时间为 KEY_SHORT_INTERVAL_TIME ~ KEY_SHORT_INTERVAL_TIME * 2
6、重复几次按键，表示几号指令

*/







#define KEY_LONG_PRESS_TIME     (1000)  //触发逻辑的长按时间
#define KEY_SHORT_PRESS_TIME    (200)   //每个短按的按压时间需少于此值
#define KEY_SHORT_PRESS_COUNT   (3)     //指定短按的个数
#define KEY_SHORT_INTERVAL_TIME (300)   //新按下和上次松开的间隔时间，大于此值判断为长间隔时间，否则为短间隔时间




//逻辑状态机
static enum {
    HIDE_TRIG_SM_IDLE = 0,      //空闲
    HIDE_TRIG_SM_SHORT_KEYS,    //短按检测中
    HIDE_TRIG_SM_COMMAND,       //判断指令号
} hide_trig_sm = HIDE_TRIG_SM_IDLE;
APP_TIMER_DEF(hide_trig_timer_id);
static uint32_t m_is_hide_trig_timer_runing = 0; //定时器是否在运行标志
static uint32_t m_long_press_time_ms;//长按的时间
static uint32_t m_interval_time_ms;//新按下和上次松开的间隔时间
static uint32_t m_short_key_count;//短按的次数，也用于指令号




//停止定时器
static void hide_trig_timer_stop(void)
{
    ret_code_t err_code;

    if (m_is_hide_trig_timer_runing != 0) {
        err_code = app_timer_stop(hide_trig_timer_id);
        APP_ERROR_CHECK(err_code);
        m_is_hide_trig_timer_runing = 0;
    }
}


//重启定时器
static void hide_trig_timer_restart(uint32_t time_ms)
{
    ret_code_t err_code;

    err_code = app_timer_stop(hide_trig_timer_id);
    APP_ERROR_CHECK(err_code);
    err_code = app_timer_start(hide_trig_timer_id, APP_TIMER_TICKS(time_ms), NULL);
    APP_ERROR_CHECK(err_code);
    m_is_hide_trig_timer_runing = 1;
}


//回到IDLE状态
static void hide_trig_idle(void)
{
    hide_trig_sm = HIDE_TRIG_SM_IDLE;
    //NRF_LOG_INFO("HIDE_TRIG_SM_IDLE");
    m_long_press_time_ms = 0;
    m_interval_time_ms = 0;
    m_short_key_count = 0;
    hide_trig_timer_stop();
}


//定时器回调
static void hide_trig_timeout_handler(void * p_context)
{
    m_interval_time_ms += 10;

    //超时，终止判断
    if (m_interval_time_ms > KEY_LONG_PRESS_TIME) {
        hide_trig_idle();
    }
    //短按检测中
    if (hide_trig_sm == HIDE_TRIG_SM_SHORT_KEYS) {
        if (m_interval_time_ms > KEY_SHORT_INTERVAL_TIME * 3) {
            //NRF_LOG_INFO("m_interval_time_ms = %d", m_interval_time_ms);
            hide_trig_idle();
        }
    }
    //判断指令号
    if (hide_trig_sm == HIDE_TRIG_SM_COMMAND) {
        //指令号判断完成
        if ((m_short_key_count == 0 && m_interval_time_ms > (KEY_SHORT_INTERVAL_TIME * 3))
            || (m_short_key_count > 0 && m_interval_time_ms > KEY_SHORT_INTERVAL_TIME)) {
            //NRF_LOG_INFO("hide_trig cmd = %d", m_short_key_count);
            //上报事件
            ui_evt_t ui_evt;
            ui_evt.ui_evt_type = UI_EVT_TYPE_HIDE_TRIG;
            ui_evt.evt.ui_evt_hide_trig.command = m_short_key_count;
            sm_event(ui_evt);
            hide_trig_idle();
        }
    }
}


//按键事件输入
void hide_trig_input(ui_evt_t ui_evt)
{
    if (ui_evt.evt.ui_evt_key.key_instance != KEY_INSTANCE_KEY_USER) { //只使用到了用户按键
        return;
    }

    key_evt_type_t key_evt_type = ui_evt.evt.ui_evt_key.key_evt_type;//按键类型
    uint32_t key_pressed_ms = ui_evt.evt.ui_evt_key.key_pressed_ms;//长按时间
    //NRF_LOG_INFO("key_evt_type = %d, key_pressed_ms = %4d, m_interval_time_ms = %4d", key_evt_type, key_pressed_ms, m_interval_time_ms);

    //空闲，做长按检测
    if (hide_trig_sm == HIDE_TRIG_SM_IDLE) {
        //如果是长按事件，保存长按时间
        if (key_evt_type == KEY_EVENT_TYPE_LONG_PRESS) {
            m_long_press_time_ms = key_pressed_ms;
        } else if (key_evt_type == KEY_EVENT_TYPE_RELEASED) {
            if (m_long_press_time_ms > KEY_LONG_PRESS_TIME) {
                hide_trig_sm = HIDE_TRIG_SM_SHORT_KEYS;//转到下一状态
                //NRF_LOG_INFO("HIDE_TRIG_SM_SHORT_KEYS");
                m_interval_time_ms = 0;
                hide_trig_timer_restart(10);//启动定时器
            }
        }
    }
    //短按检测中
    else if (hide_trig_sm == HIDE_TRIG_SM_SHORT_KEYS) {
        //如果是长按事件，终止判断
        if (key_evt_type == KEY_EVENT_TYPE_LONG_PRESS) {
            hide_trig_idle();
        }
        //如果是按下事件
        else if (key_evt_type == KEY_EVENT_TYPE_PRESSED) {
            //没达到次数，必须为短间隔
            if (m_short_key_count < 3) {
                if (m_interval_time_ms > KEY_SHORT_INTERVAL_TIME) {
                    hide_trig_idle();
                }
            }
        }
        //如果是松开事件
        else if (key_evt_type == KEY_EVENT_TYPE_RELEASED) {
            //判断一次合格的短按
            if (key_pressed_ms <= KEY_SHORT_PRESS_TIME) {
                m_short_key_count++;
                if (m_short_key_count == 3) { //达到要求的次数
                    hide_trig_sm = HIDE_TRIG_SM_COMMAND;//转到下一状态
                    //NRF_LOG_INFO("HIDE_TRIG_SM_COMMAND");
                    m_short_key_count = 0;
                }
            }
            m_interval_time_ms = 0;
        }
    }
    //判断指令号
    else if (hide_trig_sm == HIDE_TRIG_SM_COMMAND) {
        //如果是长按事件，终止判断
        if (key_evt_type == KEY_EVENT_TYPE_LONG_PRESS) {
            hide_trig_idle();
        }
        //如果是按下事件
        else if (key_evt_type == KEY_EVENT_TYPE_PRESSED) {
            //刚开始判断指令号时，第一次按下距离上次释放的时间，必须为长间隔
            if (m_short_key_count == 0) {
                if (m_interval_time_ms < KEY_SHORT_INTERVAL_TIME) {
                    hide_trig_idle();
                }
            }
            //后续的按键间隔必须为短间隔
            else {
                if (m_interval_time_ms > KEY_SHORT_INTERVAL_TIME) {
                    hide_trig_idle();
                }
            }
        }
        //如果是松开事件
        else if (key_evt_type == KEY_EVENT_TYPE_RELEASED) {
            //判断一次合格的短按
            if (key_pressed_ms <= KEY_SHORT_PRESS_TIME) {
                m_short_key_count++;
                if (m_short_key_count == 3) { //达到要求的次数
                    hide_trig_sm = HIDE_TRIG_SM_COMMAND;//转到下一状态
                    //NRF_LOG_INFO("HIDE_TRIG_SM_COMMAND");
                }
            }
            m_interval_time_ms = 0;
        }
    }

    //连续单击检测
    if (hide_trig_sm == HIDE_TRIG_SM_IDLE) { //仅在没有做隐藏按键检测时检测用户连按
        static uint32_t last_release_tick = 0;//上次释放按键的时刻
        static uint32_t multi_clicks_num = 0;//已经连续按键的次数

        if (ui_evt.ui_evt_type == UI_EVT_TYPE_KEY
            && ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER
            && ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_RELEASED) {
            if ((sm_get_power_on_time() > last_release_tick) && (sm_get_power_on_time() - last_release_tick <= 300)) { //距上次松手不超过300ms
                //认为是一次连续按键
                if (++multi_clicks_num == 3) { //够5次
                    multi_clicks_num = 0;
                    //NRF_LOG_INFO("FIVE_KEY");
                    //上报事件
                    ui_evt_t ui_evt;
                    ui_evt.ui_evt_type = UI_EVT_TYPE_HIDE_TRIG;
                    ui_evt.evt.ui_evt_hide_trig.command = 0xFF;
                    sm_event(ui_evt);
                    hide_trig_idle();
                }
            } else {
                multi_clicks_num = 0;
            }
            last_release_tick = sm_get_power_on_time();
        }
    }
}


//初始化
void hide_trig_init(void)
{
    hide_trig_sm = HIDE_TRIG_SM_IDLE;

    //定时器
    ret_code_t err_code = app_timer_create(&hide_trig_timer_id, APP_TIMER_MODE_REPEATED, hide_trig_timeout_handler);
    APP_ERROR_CHECK(err_code);
}

