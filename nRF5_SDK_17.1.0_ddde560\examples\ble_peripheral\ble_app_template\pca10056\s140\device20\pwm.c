#include "pwm.h"
#include "nrf_drv_pwm.h"
#include "app_pwm.h"

APP_PWM_INSTANCE(PWM2, 2);
static nrf_drv_pwm_t m_pwm_led = NRF_DRV_PWM_INSTANCE(0);//gb
static nrf_pwm_values_individual_t m_pwm_b_g_seq_values;
static nrf_pwm_sequence_t const    m_pwm_b_g_seq = {
    .values.p_individual = &m_pwm_b_g_seq_values,
    .length              = NRF_PWM_VALUES_LENGTH(m_pwm_b_g_seq_values),
    .repeats             = 0,
    .end_delay           = 0
};

//状态指示灯
#define LED_PIN  24  // 连接 DIN 的 GPIO 引脚
#define PWM_TOP  20  // 16MHz / 20 = 0.8MHz，每tick=1.25us

static nrf_drv_pwm_t m_pwm1 = NRF_DRV_PWM_INSTANCE(1);
static nrf_pwm_sequence_t const *p_sequence;

#define BITS_PER_LED_CMD   24
#define LED_COUNT          1
#define RESET_BITS         100   // 50*1.25us=62.5us，接近复位要求

// WS2812/TX1812 一位的占空比定义 (20 ticks = 1.25us)
#define T1H  (0x8000 | 13)  // ≈0.7us 高电平
#define T0H  (0x8000 | 3)   // ≈0.2us 高电平
#define TLO  (0x8000 | 0)   // 低电平

static uint16_t m_buffer[BITS_PER_LED_CMD * LED_COUNT + RESET_BITS];


void ws2812_init(void) {
    nrf_drv_pwm_config_t const config1 =
    {
        .output_pins =
        {
            LED_PIN | NRF_DRV_PWM_PIN_INVERTED, // 反相输出更贴近WS2812要求
            NRF_DRV_PWM_PIN_NOT_USED,
            NRF_DRV_PWM_PIN_NOT_USED,
            NRF_DRV_PWM_PIN_NOT_USED,
        },
        .irq_priority = APP_IRQ_PRIORITY_LOWEST,
        .base_clock   = NRF_PWM_CLK_16MHz,
        .count_mode   = NRF_PWM_MODE_UP,
        .top_value    = PWM_TOP,
        .load_mode    = NRF_PWM_LOAD_COMMON,
        .step_mode    = NRF_PWM_STEP_AUTO
    };
    APP_ERROR_CHECK(nrf_drv_pwm_init(&m_pwm1, &config1, NULL));
	
	ws2812_show(0,0,0);
}

void ws2812_show(uint8_t r, uint8_t g, uint8_t b) {
    uint32_t idx = 0;

  
    uint8_t brightness_r = (uint8_t)((r * 26) / 255);   // 10% = 26/255
    uint8_t brightness_g = (uint8_t)((g * 26) / 255);   // 10% = 26/255
    uint8_t brightness_b = (uint8_t)((b * 26) / 255);   // 10% = 26/255

    // 颜色校准 - 调整各颜色分量以获得更纯的白色
    uint8_t calibrated_r = (uint8_t)((brightness_r * 120) / 255);  // 红色降低到47%
    uint8_t calibrated_g = (uint8_t)((brightness_g * 120) / 255);  // 绿色降低到47%
    uint8_t calibrated_b = (uint8_t)((brightness_b * 180) / 255);  // 蓝色提升到71%

    // RGB 顺序，修正为正确的颜色映射
    uint8_t colors[3] = {brightness_g, brightness_r, brightness_b};

    for (int c = 0; c < 3; c++) {
        for (int bit = 7; bit >= 0; bit--) {
            if (colors[c] & (1 << bit)) {
				m_buffer[idx++] = T1H;
			} else {
				m_buffer[idx++] = T0H;
			}
        }
    }

    // 复位信号填充 0
    for (int i = 0; i < RESET_BITS; i++) {
		m_buffer[idx++] = TLO;
	}

    nrf_pwm_sequence_t const seq =
    {
        .values.p_common = m_buffer,
        .length          = idx,
        .repeats         = 0,
        .end_delay       = 0
    };

    (void)nrf_drv_pwm_simple_playback(&m_pwm1, &seq, 1,
                                      NRF_DRV_PWM_FLAG_LOOP);
}

//PWM
//PWM频率80kHz
void pwm_led_init(void)
{
	  nrf_gpio_cfg_output(LED_G_CUT_PIN);
    nrf_gpio_pin_clear(LED_G_CUT_PIN);

    nrf_gpio_cfg_output(LED_B_CUT_PIN);
    nrf_gpio_pin_clear(LED_B_CUT_PIN);
    nrf_drv_pwm_config_t const config = {
        .output_pins =
        {
            PWM_LED_PIN,             // channel 0
            NRF_DRV_PWM_PIN_NOT_USED,   // channel 1
            NRF_DRV_PWM_PIN_NOT_USED,   // channel 2
            NRF_DRV_PWM_PIN_NOT_USED    // channel 3
        },
        .irq_priority = APP_IRQ_PRIORITY_LOWEST,
        .base_clock   = NRF_PWM_CLK_16MHz,      //时钟
        .count_mode   = NRF_PWM_MODE_UP,
        .top_value    = 100,                   //周期，最大值是0x7FFF
        .load_mode    = NRF_PWM_LOAD_INDIVIDUAL,
        .step_mode    = NRF_PWM_STEP_AUTO
    };
    APP_ERROR_CHECK(nrf_drv_pwm_init(&m_pwm_led, &config, NULL));

    //比较值
    m_pwm_b_g_seq_values.channel_0 = 100;
    m_pwm_b_g_seq_values.channel_1 = 0;
    m_pwm_b_g_seq_values.channel_2 = 0;
    m_pwm_b_g_seq_values.channel_3 = 0;

    (void)nrf_drv_pwm_simple_playback(&m_pwm_led, &m_pwm_b_g_seq, 1, NRF_DRV_PWM_FLAG_LOOP);

}


//PWM_YELLOE和PWM_BLUE反初始化
void pwm_led_deinit(void)
{
    nrf_drv_pwm_uninit(&m_pwm_led);
//    nrf_gpio_cfg_input(PWM_BLUE_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(PWM_LED_PIN, NRF_GPIO_PIN_NOPULL);
}

//设置PWM_GREEN占空比
//duty: 0~100
void pwm_led_set_duty(uint16_t duty)
{
    APP_ERROR_CHECK_BOOL(duty <= 100);
    m_pwm_b_g_seq_values.channel_0 = 100 - duty;
}

//亮蓝灯
void blue_light(void){
	ws2812_show(0, 0, 255);
}

//亮白灯
void write_light(void){
	ws2812_show(255, 255, 255);
}

//关灯
void close_light(void){
	ws2812_show(0, 0, 0);
}