#include "i2c_slave.h"
#include "nrf_drv_twis.h"
//#include "factory.h"


#define I2C_BUF_SIZE                   (128)



static nrf_drv_twis_t m_twis = NRF_DRV_TWIS_INSTANCE(1);



//I2C接收缓存
static uint8_t m_rxbuff[I2C_BUF_SIZE + 1];
//可以被读走的数据地址和长度
uint8_t* i2c_data_addr;
uint32_t i2c_data_len;
//等待被读取的I2C数据
static struct {
    uint32_t pcba_test_flag;
    uint8_t pcba_mac_addr[6];
    uint32_t analog_data[3];
} buf_data;


//I2C事件回调
static void twis_event_handler(nrf_drv_twis_evt_t const * const p_event)
{
    ret_code_t ret_code;

    switch (p_event->type) {
    case TWIS_EVT_READ_REQ://主机要读数据
        if (p_event->data.buf_req) {
            //准备要被读走的数据
            //NRF_LOG_INFO("i2c_data_len = %d", i2c_data_len);
            ret_code = nrf_drv_twis_tx_prepare(&m_twis, i2c_data_addr, i2c_data_len);
            APP_ERROR_CHECK(ret_code);
        }
        break;
    case TWIS_EVT_READ_DONE://主机读完数据
        //NRF_LOG_INFO("TWIS_EVT_READ_DONE, %d", p_event->data.tx_amount);
        break;
    case TWIS_EVT_WRITE_REQ://主机要写数据
        if (p_event->data.buf_req) {
            ret_code = nrf_drv_twis_rx_prepare(&m_twis, m_rxbuff, sizeof(m_rxbuff));
            APP_ERROR_CHECK(ret_code);
        }
        break;
    case TWIS_EVT_WRITE_DONE://主机写完数据
        //NRF_LOG_INFO("TWIS_EVT_WRITE_DONE, %d", p_event->data.rx_amount);
        //NRF_LOG_HEXDUMP_INFO(m_rxbuff, p_event->data.rx_amount);
        //处理数据
        i2c_recv_package(m_rxbuff, p_event->data.rx_amount);
        break;

    case TWIS_EVT_READ_ERROR:
    case TWIS_EVT_WRITE_ERROR:
    case TWIS_EVT_GENERAL_ERROR:
        //m_error_flag = true;
        break;
    default:
        break;
    }
}


//I2C从机初始化
void i2c_slave_init(void)
{
    ret_code_t ret_code;

    const nrf_drv_twis_config_t config = {
        .addr               = {TWIS_SLAVE_ADDR, 0},
        .scl                = TX_PIN,
        .scl_pull           = NRF_GPIO_PIN_PULLUP,
        .sda                = RX_PIN,
        .sda_pull           = NRF_GPIO_PIN_PULLUP,
        .interrupt_priority = APP_IRQ_PRIORITY_HIGH
    };

    ret_code = nrf_drv_twis_init(&m_twis, &config, twis_event_handler);
    APP_ERROR_CHECK(ret_code);
    nrf_drv_twis_enable(&m_twis);
}


//更新PCBA产测通过标志位
void i2c_slave_update_test_flag(uint32_t flag)
{
    buf_data.pcba_test_flag = flag;
    i2c_data_addr = (uint8_t*)&buf_data.pcba_test_flag;
    i2c_data_len = 20;
}


//更新PCBA的MAC地址
void i2c_slave_update_mac_addr(uint8_t* addr)
{
    for (int32_t i = 0; i < 6; i++) {
        buf_data.pcba_mac_addr[i] = addr[i];
    }
    i2c_data_addr = buf_data.pcba_mac_addr;
    i2c_data_len = 6;
}


//更新采样数据
void i2c_slave_update_analog_data(uint32_t dark, uint32_t green, uint32_t blue)
{
    buf_data.analog_data[0] = dark;
    buf_data.analog_data[1] = green;
    buf_data.analog_data[2] = blue;
    i2c_data_addr = (uint8_t*)buf_data.analog_data;
    i2c_data_len = 12;
}



